"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'unitTplUser';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        school: { type: String, required: true },
        name: { type: String, required: true },
        curriculum: { type: String, required: true },
        tpl: {
            // auto create, curriculum -> sys curriculum -> copy sys unit+task tpl -> save here
            unit: { type: String },
            task: { type: String }, // unit-tpl._id
        },
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
