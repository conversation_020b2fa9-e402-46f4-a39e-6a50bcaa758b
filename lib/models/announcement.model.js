"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'announcement';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        from: { type: String, trim: true },
        to: { type: [String], trim: true },
        at: { type: String, enum: ['all'], default: 'all' },
        school: { type: String, trim: true },
        class: { type: String, trim: true },
        message: { type: String },
        attachments: {
            // 图片/视频证据
            filename: { type: String, trim: true },
            mime: { type: String, trim: true },
            hash: { type: String, trim: true }, // 文件SHA1, files._id
        },
        readList: { type: [String] }, // 已读uid列表
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
