"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const PDFDocument = require("pdfkit-table");
function default_1(app) {
    app.all('/pdf/invoice', async (req, res) => {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        const get = Object.assign({}, req.query, req.body);
        let doc = new PDFDocument({ margin: 30, size: 'A4' });
        res.set('Content-Type', 'application/pdf');
        doc.moveDown();
        doc.fontSize(14);
        doc.font("Helvetica-Bold").text(`Academic Ezy Group Ltd`, { align: 'right' });
        doc.font("Helvetica").fontSize(10);
        doc.text(`Level12, 19 Victoria St Auckland`, { align: 'right' });
        doc.text(`CBD Auckland New Zealand`, { align: 'right' });
        doc.text(`Phone: 09 3681231`, { align: 'right' });
        doc.text(`Email: <EMAIL>`, { align: 'right' });
        doc.text(`GST No. ***********`, { align: 'right' });
        doc.moveDown();
        doc.moveDown().fontSize(12).font("Helvetica-Bold")
            .fillColor('black').highlight(25, doc.y - 10, 545, 45)
            .text(`Invoice NO. ${(_a = get.no) !== null && _a !== void 0 ? _a : ''}`, { align: 'justify' });
        doc.moveUp().text('Tax Invoice', { align: 'right' });
        doc.font("Helvetica").fontSize(10).text(`Invoice Date:  ${(_b = get.date) !== null && _b !== void 0 ? _b : ''}`, { align: 'right' });
        doc.moveDown().moveDown().fontSize(10).text(`Bill to:  ${(_c = get.schoolName) !== null && _c !== void 0 ? _c : ''}`, { align: 'justify' });
        doc.moveDown();
        const table = {
            title: "",
            subtitle: "",
            headers: [
                { label: "DESCRIPTION", property: 'desc', width: 280, renderer: null },
                { label: "UNITS", property: 'units', width: 80, renderer: null },
                { label: "UNIT PRICE\n(Exl.GST)", property: 'price', width: 60, renderer: null },
                { label: "TAX TYPE\nGST", property: 'type', width: 60, renderer: null },
                { label: "AMOUNT\n(Exl.GST)", property: 'amount', width: 60, renderer: null },
            ],
            datas: (_d = get.list) !== null && _d !== void 0 ? _d : [],
        };
        doc.table(table, {
            prepareHeader: () => doc.font("Helvetica-Bold").fontSize(10),
            prepareRow: () => {
                doc.font("Helvetica").fontSize(10);
            },
        });
        doc.text(`GST Amount         ${(_e = get.amount) !== null && _e !== void 0 ? _e : ''}`, { align: 'right' });
        doc.text(`Total(inc.GST)         ${(_f = get.total) !== null && _f !== void 0 ? _f : ''}`, { align: 'right' });
        if (get.discount)
            doc.text(`Discounted amount         ${(_g = get.discount) !== null && _g !== void 0 ? _g : ''}`, { align: 'right' });
        doc.text(`Amount paid         ${(_h = get.paid) !== null && _h !== void 0 ? _h : ''}`, { align: 'right' });
        doc.text(`Amount DUE         ${(_j = get.due) !== null && _j !== void 0 ? _j : ''}`, { align: 'right' });
        if (get.discount)
            doc.moveDown().text('Notice: Your school has chosen the discount of Classcipe resource-sharing option, which means all your school resource center content will be automatically published to Classcipe library without further authorization or noticement upon applying the discount to the plan fee. ');
        doc.moveDown().moveDown().fontSize(12).text(`Notes`, { align: 'left' });
        doc.moveDown().fontSize(10).text(`Please arrange payment to the bank account:`, { align: 'left' });
        doc.text(`Academic EZY Group Limited`, { align: 'left' });
        doc.text(`01-1811-********-00`, { align: 'left' });
        doc.pipe(res);
        doc.end();
    });
}
exports.default = default_1;
