"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'servicePackSchoolPrice';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        school: { type: String, required: true },
        servicePack: { type: String, required: true },
        priceEnable: { type: Boolean, default: false },
        contentOrientated: [
            {
                premium: { type: String },
                times: { type: Number },
                price: { type: Number, trim: true },
                schoolPrice: { type: Number, trim: true },
                servicePack: { type: String }, // 捆绑的服务包, service-pack._id 服务包id
            },
        ],
        deadline: { type: Date },
        withinSchool: { type: Boolean, default: false },
        students: { type: [String] },
        teachers: { type: [String] },
        role: { type: String, default: 'student', enum: ['student', 'teacher'] },
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
