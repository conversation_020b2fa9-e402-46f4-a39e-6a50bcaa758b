"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const classes_class_1 = require("./classes.class");
const classes_model_1 = __importDefault(require("../../models/classes.model"));
const classes_hooks_1 = __importDefault(require("./classes.hooks"));
function default_1(app) {
    const options = {
        Model: (0, classes_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/classes', new classes_class_1.Classes(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('classes');
    service.hooks(classes_hooks_1.default);
}
exports.default = default_1;
