"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'teachingAccident';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        student: { type: String, required: true },
        teacher: { type: String, required: true },
        session: { type: String },
        sessionName: { type: String },
        serviceType: { type: String },
        servicePackUser: { type: String },
        serviceBooking: { type: String },
        service: { type: String },
        serviceName: { type: String },
        // 学生申诉
        evidencesStudent: [
            {
                content: { type: String },
                attachments: [
                    {
                        // 图片/视频证据
                        filename: { type: String, trim: true },
                        mime: { type: String, trim: true },
                        hash: { type: String, trim: true }, // 文件SHA1, files._id
                    },
                ],
            },
        ],
        // 老师申诉
        evidencesTeacher: [
            {
                content: { type: String },
                attachments: [
                    {
                        // 图片/视频证据
                        filename: { type: String, trim: true },
                        mime: { type: String, trim: true },
                        hash: { type: String, trim: true }, // 文件SHA1, files._id
                    },
                ],
            },
        ],
        status: { type: String, default: 'pending', enum: ['pending', 'approved', 'rejected'] },
        approvedAt: { type: Date },
        checkReason: { type: String },
        read: { type: Boolean, default: false },
        tags: { type: [String] },
        serviceReturn: { type: Boolean, default: false },
        days: { type: Number }, // 停课天数
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
