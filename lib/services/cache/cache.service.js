"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cache_class_1 = require("./cache.class");
const cache_model_1 = __importDefault(require("../../models/cache.model"));
const cache_hooks_1 = __importDefault(require("./cache.hooks"));
function default_1(app) {
    const options = {
        Model: (0, cache_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/cache', new cache_class_1.Cache(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('cache');
    service.hooks(cache_hooks_1.default);
}
exports.default = default_1;
