"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const class_apply_class_1 = require("./class-apply.class");
const class_apply_model_1 = __importDefault(require("../../models/class-apply.model"));
const class_apply_hooks_1 = __importDefault(require("./class-apply.hooks"));
function default_1(app) {
    const options = {
        Model: (0, class_apply_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/class-apply', new class_apply_class_1.ClassApply(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('class-apply');
    service.hooks(class_apply_hooks_1.default);
}
exports.default = default_1;
