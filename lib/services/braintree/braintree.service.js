"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const braintree_class_1 = require("./braintree.class");
const braintree_hooks_1 = __importDefault(require("./braintree.hooks"));
function default_1(app) {
    const options = {
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/braintree', new braintree_class_1.Braintree(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('braintree');
    service.hooks(braintree_hooks_1.default);
}
exports.default = default_1;
