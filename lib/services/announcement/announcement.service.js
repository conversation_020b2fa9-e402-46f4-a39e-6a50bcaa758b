"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const announcement_class_1 = require("./announcement.class");
const announcement_model_1 = __importDefault(require("../../models/announcement.model"));
const announcement_hooks_1 = __importDefault(require("./announcement.hooks"));
function default_1(app) {
    const options = {
        Model: (0, announcement_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/announcement', new announcement_class_1.Announcement(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('announcement');
    service.hooks(announcement_hooks_1.default);
}
exports.default = default_1;
