"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'sessionTakeaway';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, required: true },
        session: { type: String, required: true },
        page: { type: String },
        question: { type: String },
        score: { type: Number },
        point: { type: Number },
        points: { type: Schema.Types.Mixed },
        outlines: {
            outline: { type: Schema.Types.Mixed },
            assess: { type: Schema.Types.Mixed },
            pd: { type: Schema.Types.Mixed },
            goal: { type: Schema.Types.Mixed },
            skills: { type: Schema.Types.Mixed }, // skills
        },
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
