"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'poster';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        image: { type: String, trim: true },
        imageCover: { type: String, trim: true },
        content: { type: String },
        type: { type: String, default: 'image', enum: ['image', 'text', 'customize'] },
        style: { type: String, default: 'educator', enum: ['educator', 'non_educator', 'verified_teacher'] },
        customize: { type: <PERSON><PERSON><PERSON>, default: false }, //海报是否自定义
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
