"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'criteriaWeight';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        tab: { type: String, enum: ['teacherTraining', 'teacherTrainingSubjects'] },
        uid: { type: String, required: true },
        names: { type: [String] },
        hours: { type: Number, required: true },
        assessment: { type: String },
        data: [
            {
                taskCategory: { type: String, required: true },
                weight: { type: Number },
                assessments: { type: [Number] }, // [20, 20, ...] no of assessments required 考核数及权重
            },
        ],
        thesisDefense: { type: Boolean, default: false }, // Thesis defense按钮开关 是否要求论文答辩
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
