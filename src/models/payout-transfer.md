I am using feathers + mongodb. I also want to implement Airwallex for payouts.
I have already created a page in account settings where our users their payout details(airwallex beneficiary id), then we will store them
in our db in payout-accounts collection ({userId, airwallexBeneficiaryId, isActive: true, lastPayoutDate, totalPayoutAmount, payoutCount})
Then later on monthly basis we will check their income balance(which is maintained in our db in unit cents dollar in user collection)
to send them the amount of money if balance is greater than 5 dollars.
So we will send payout on 5th of every month, and on last day of previous month(28,29,30 or 31) I want to check the amount, and only send payout
to those who have balance greater than 5 usd at this point of time, so we need to store this somewhere(should we store this in payout-accounts collection???).
I will be fetching this list and render in my admin dashboard and see/analyze the payout list/total amount and edit/cancel an individual user;s payout if required and then add funds to airwallex account based on it the day before scheduled payout(4th of every month).
Also after transfer is done I want to store the transaction id(or handle any errors) and other details somewhere so that we can track the payout history.
Should we create a new collection for this or add in payout-accounts collection itself???
