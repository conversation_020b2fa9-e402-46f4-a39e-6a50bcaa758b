"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'section';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        name: { type: String, trim: true, required: true },
        prompt: { type: String, trim: true },
        salesPrice: { type: Number, default: 0 },
        costPrice: { type: Number, default: 0 },
        uid: { type: Schema.Types.ObjectId, ref: 'users' },
        status: { type: String, enum: ['pending', 'ongoing', 'completed'], default: 'pending' },
        serviceTaskId: { type: Schema.Types.ObjectId, ref: 'servicePack' },
        markAsCompleted: { type: Boolean, default: false },
        lastAssignedTime: { type: Date },
        credit: [
            {
                userId: { type: Schema.Types.ObjectId, ref: 'users' },
                points: { type: Number, default: 0 }, // Credit points for the user
            },
        ],
        files: [
            {
                userId: { type: Schema.Types.ObjectId, ref: 'users' },
                name: [{ type: String, trim: true }],
                nickname: { type: String, trim: true },
                avatar: { type: String },
                cover: { type: String, trim: true },
                uploadedTime: { type: Date, default: Date.now }, // Upload time
            },
        ],
        comments: [
            {
                userId: { type: Schema.Types.ObjectId, ref: 'users' },
                name: [{ type: String, trim: true }],
                nickname: { type: String, trim: true },
                avatar: { type: String },
                comment: { type: String, trim: true },
                createdAt: { type: Date, default: Date.now }, // Comment creation time
            },
        ],
    }, {
        timestamps: true,
    });
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
