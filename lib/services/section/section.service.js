"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const section_class_1 = require("./section.class");
const section_model_1 = __importDefault(require("../../models/section.model"));
const section_hooks_1 = __importDefault(require("./section.hooks"));
function default_1(app) {
    const options = {
        Model: (0, section_model_1.default)(app),
        whitelist: ['$exists', '$regex', '$options'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/section', new section_class_1.Section(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('section');
    service.hooks(section_hooks_1.default);
}
exports.default = default_1;
