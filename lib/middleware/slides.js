"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const { google } = require('googleapis');
function default_1(app) {
    const authConf = app.get('googleAuth');
    const oauth2Client = new google.auth.OAuth2(...authConf);
    const slides = google.slides({ version: 'v1', auth: oauth2Client });
    const slideInit = async (googleId) => {
        var _a;
        const redis = app.get('redis');
        const { token, exp, rt } = JSON.parse((_a = (await redis.get(`googleAuth:${googleId}`))) !== null && _a !== void 0 ? _a : '{}');
        if (!token)
            return { message: 'googleId error', googleId };
        if (exp < Date.now() && !rt)
            return { message: 'token expire', googleId };
        const oauth2Client = new google.auth.OAuth2(...authConf);
        const gconf = exp < Date.now() ? { refresh_token: rt } : { access_token: token };
        oauth2Client.setCredentials(gconf);
        return google.slides({ version: 'v1', auth: oauth2Client });
    };
    const mod = {
        async get(gid, presentationId) {
            const slides = await slideInit(gid);
            if (slides.message)
                return slides;
            const page = await slides.presentations.get({ presentationId });
            return page;
        },
        async pageGet(gid, presentationId, pageObjectId) {
            const cache = app.service('cache');
            const _id = Acan.SHA1([gid, presentationId].join(':'));
            const doc = await cache.Model.findById(_id);
            if (doc) {
                const page = doc.data.slides.find((v) => v.objectId === pageObjectId);
                return page;
            }
            const slides = await slideInit(gid);
            if (slides.message)
                return slides;
            const { data } = await slides.presentations.pages.get({ presentationId, pageObjectId }).catch((e) => {
                return e;
            });
            return data;
        },
        pageRequest(page) {
            const requests = [];
            page.pageElements.map((ele) => {
                if (ele.shape) {
                    requests.push({
                        createShape: {
                            objectId: ele.objectId,
                            elementProperties: {
                                pageObjectId: page.objectId,
                                size: ele.size,
                                transform: ele.transform,
                            },
                            shapeType: ele.shape.shapeType,
                        },
                        // updateShapeProperties: {
                        //   objectId: ele.objectId,
                        //   shapeProperties: ele.shape.shapeProperties,
                        //   fields: '*'
                        // }
                    });
                }
                if (ele.image) {
                    requests.push({
                        createImage: {
                            objectId: ele.objectId,
                            url: ele.image.contentUrl,
                            elementProperties: {
                                pageObjectId: page.objectId,
                                size: ele.size,
                                transform: ele.transform,
                            },
                        },
                        // updateImageProperties: {
                        //   objectId: ele.objectId,
                        //   imageProperties: ele.image.imageProperties,
                        //   fields: '*'
                        // }
                    });
                }
            });
            // requests.push({
            //   updateSlideProperties: {
            //     objectId: page.objectId,
            //     slideProperties: page.slideProperties,
            //     fields: '*'
            //   }
            // })
            return requests;
        },
    };
    app.all('/google/get/:id', async (req, res) => {
        var _a, _b, _c;
        const { googleId } = (_a = req.query) !== null && _a !== void 0 ? _a : {};
        const cache = app.service('cache');
        const slides = await slideInit(googleId);
        if (slides.message)
            return res.json({ message: slides.message, googleId });
        const { id } = (_b = req.params) !== null && _b !== void 0 ? _b : {};
        const _id = Acan.SHA1([googleId, id].join(':'));
        const doc = await cache.Model.findById(_id);
        if (doc)
            return res.json(doc.data);
        const rs = await slides.presentations.get({ presentationId: id }).catch((e) => {
            var _a;
            return (_a = e.message) !== null && _a !== void 0 ? _a : e.stack;
        });
        cache.Model.updateOne({ _id }, { data: rs.data }, { upsert: true }).then();
        res.json((_c = rs.data) !== null && _c !== void 0 ? _c : rs);
    });
    app.all('/google/add/:id', async (req, res) => {
        var _a, _b, _c;
        const { id } = (_a = req.params) !== null && _a !== void 0 ? _a : {};
        const { id: pid, pageId, googleId, gid } = (_b = req.query) !== null && _b !== void 0 ? _b : {};
        const nt = Date.now();
        const page = await mod.pageGet(gid, pid, pageId);
        if (page.message)
            return res.json({ message: page.message, gid, id, pageId, ttl: Date.now() - nt });
        console.log(Object.keys(page), 'old page');
        const requests = mod.pageRequest(page);
        const slides = await slideInit(googleId);
        if (slides.message)
            return res.json({ message: slides.message, googleId, ttl: Date.now() - nt });
        const rs = await slides.presentations.batchUpdate({ presentationId: id, resource: { requests } }).catch((e) => {
            var _a;
            return (_a = e.message) !== null && _a !== void 0 ? _a : e.stack;
        });
        res.json((_c = rs.data) !== null && _c !== void 0 ? _c : rs);
    });
    app.all('/google/get/:id/:pageId', async (req, res) => {
        var _a, _b, _c;
        const { id, pageId } = (_a = req.params) !== null && _a !== void 0 ? _a : {};
        const { googleId } = (_b = req.query) !== null && _b !== void 0 ? _b : {};
        const cache = app.service('cache');
        const _id = Acan.SHA1([googleId, id].join(':'));
        const doc = await cache.Model.findById(_id);
        if (doc) {
            const page = doc.data.slides.find((v) => v.objectId === pageId);
            const requests = mod.pageRequest(page);
            if (page)
                return res.json({ requests, page });
        }
        const rs = await req.slides.presentations.pages.get({ presentationId: id, pageObjectId: pageId }).catch((e) => {
            var _a;
            return (_a = e.message) !== null && _a !== void 0 ? _a : e.stack;
        });
        res.json((_c = rs.data) !== null && _c !== void 0 ? _c : rs);
    });
}
exports.default = default_1;
