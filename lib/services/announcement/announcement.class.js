"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Announcement = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
class Announcement extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async extUser(one, params) {
        one.fromInfo = await this.app.service('users').uidToInfo(one.from);
    }
    async getRead({ class: classId, school }, params) {
        var _a, _b;
        return await this.Model.updateMany({ class: classId, school, to: (_a = params === null || params === void 0 ? void 0 : params.user) === null || _a === void 0 ? void 0 : _a._id }, { $addToSet: { readList: (_b = params === null || params === void 0 ? void 0 : params.user) === null || _b === void 0 ? void 0 : _b._id } });
    }
}
exports.Announcement = Announcement;
