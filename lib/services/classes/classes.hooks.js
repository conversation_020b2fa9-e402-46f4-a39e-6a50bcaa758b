"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [
            (d) => {
                var _a, _b;
                const query = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
                const user = (_b = d.params.user) !== null && _b !== void 0 ? _b : {};
                if (query.$sys) {
                    if (!hook_1.default.roleHas(['sys', 'admin'])(d))
                        return (d.result = null), d;
                    delete query.$sys;
                }
                else if (!query.school)
                    query.school = user._id;
                else if (Array.isArray(query.school)) {
                    query.school = query.school.map((v) => {
                        if (v === null)
                            return user._id;
                        else
                            return v;
                    });
                }
            },
        ],
        get: [hook_1.default.toClass],
        create: [
            (d) => {
                var _a;
                const user = (_a = d.params.user) !== null && _a !== void 0 ? _a : {};
                if (d.data.type === 'subject') {
                    d.data.host = user._id;
                }
            },
        ],
        update: [hook_1.default.disable],
        patch: [],
        remove: [],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                await d.service.autoCreatePersonalClass(d.result, d.params);
            },
        ],
        get: [],
        create: [
            async (d) => {
                var _a;
                const user = (_a = d.params.user) !== null && _a !== void 0 ? _a : {};
                await d.service.count(d.result, d.params);
                // 创建者老师自动加入该学科班的老师列表
                if (d.result.type === 'subject') {
                    let schoolUser = await await d.app.service('school-user').Model.findOne({ uid: user._id, school: d.result.school });
                    await d.app.service('school-user').patch(schoolUser._id, { $addToSet: { class: d.result._id.toString() } });
                }
                // question logs
                if (d.data.questions) {
                    await d.app.service('class-question-logs').updateLogs({ questions: d.data.questions }, d.params);
                }
            },
        ],
        update: [],
        patch: [
            async (d) => {
                if (Acan.isDefined(d.data.del))
                    await d.service.count(d.result, d.params);
                if (d.result.type == 'standard' && d.data.del === false) {
                    // 恢复班级同时将所有学生恢复
                    await d.app.service('students').Model.updateMany({ ['class']: d.id }, { $set: { del: false } });
                    d.app.get('redis').HDEL('StatSchool', d.result.school).then();
                    let students = await d.app.service('students').Model.find({ class: d.id });
                    d.app.service('students').sendNoticeAll(students, 'StudentAddedToStandardClass', d.params);
                }
                // 归档班级同时将所有学生归档
                if (d.result.type == 'standard' && d.data.del === true) {
                    // 通知学生
                    let students = await d.app.service('students').Model.find({ class: d.id, del: false });
                    d.app.service('students').sendNoticeAll(students, 'StudentRemovedFromStandardClass', d.params);
                    await d.app.service('students').Model.updateMany({ ['class']: d.id }, { $set: { del: true } });
                    d.app.get('redis').HDEL('StatSchool', d.result.school).then();
                }
                // question logs
                if (d.data.questions) {
                    await d.app.service('class-question-logs').updateLogs({ questions: d.data.questions }, d.params);
                }
            },
        ],
        remove: [
            async (d) => {
                await d.service.count(d.result, d.params);
                if (d.result.type == 'subject') {
                    // 删除班级 清理报名记录
                    await d.app.service('class-apply').Model.deleteMany({ class: d.result._id });
                    // 通知
                    let students = await d.app.service('students').Model.find({ subjectClass: d.result._id });
                    for (let i = 0; i < students.length; i++) {
                        const item = students[i];
                        let doc = {
                            class: d.result._id,
                            className: d.result.name,
                            student: item._id,
                        };
                        d.app.service('class-apply').send(doc, 'StudentRemovedFromSubjectClass', d.params);
                    }
                }
            },
        ],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
