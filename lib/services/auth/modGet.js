"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = __importDefault(require("../../logger"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const authentication_1 = require("@feathersjs/authentication");
const { authenticate } = authentication_1.hooks;
const mod = {
    async refreshJwt(d) {
        var _a, _b;
        const user = (_a = d.params.user) !== null && _a !== void 0 ? _a : {};
        const { sid } = (_b = d.params.query) !== null && _b !== void 0 ? _b : {};
        if (!user._id)
            return (d.result = null), d;
        d.result = await d.app
            .service('authentication')
            .create({ strategy: 'sync' }, {
            payload: { sid },
            user: { ...user, id: user._id },
        })
            .catch(() => {
            return {};
        });
    },
    async syncUI(d) {
        var _a;
        const { sid } = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
        const rs = await d.app.get('redis').hGetAll('syncUI:' + sid);
        for (const key in rs) {
            try {
                rs[key] = JSON.parse(rs[key]);
            }
            catch (err) {
                logger_1.default.error(err, key, rs[key]);
            }
        }
        d.result = rs;
    },
    async pageConf(d) {
        // 获取页面配置
        const sid = d.params.query.sid;
        const del = d.params.query.del;
        const rs = await d.app.get('redis').HGETALL('pageConf:' + sid);
        for (const key in rs) {
            try {
                rs[key] = JSON.parse(rs[key]);
            }
            catch (err) {
                logger_1.default.error(err, key, rs[key]);
            }
        }
        d.result = { ...rs };
        if (del)
            d.app.get('redis').del('pageConf:' + sid);
    },
    async setSid(d) {
        d.params.connection._sid = d.params.query.sid;
        d.result = { ok: 1 };
        return d;
    },
    async proxyProfile(d) {
        var _a;
        const { sid, token } = d.params.query || {};
        const { secret } = d.app.get('authentication');
        const payload = jsonwebtoken_1.default.verify(token, secret);
        const role = (_a = payload === null || payload === void 0 ? void 0 : payload.role) !== null && _a !== void 0 ? _a : 'student';
        const profile = { role: payload.role, email: payload.username };
        if (!payload.username) {
            // token失效
            d.result = { logout: true };
            return;
        }
        profile.role = role;
        d.result = profile || {};
        if (d.params.user) {
            d.params.user.role = role;
            return d;
        }
        const user = await d.app.service('users').findByMail(profile.email);
        logger_1.default.info('proxyProfile', user.nickname, user.email, sid);
        user.role = role;
        d.result.auth = await d.app.service('authentication').create({ strategy: 'sync' }, {
            payload: { sid, role },
            user: { ...user, id: user._id },
        });
        d.service.patch(null, { on: 'connect', from: 'proxyProfile', _sid: sid, user });
    },
    async profile(d) {
        d.result = d.params.profile || {};
    },
    async info(d) {
        var _a;
        // 获取登录用户的信息
        await authenticate('jwt')(d);
        d.result = (_a = d.params.user) !== null && _a !== void 0 ? _a : { ok: 1 };
    },
    async channels(d) {
        const cn = d.params.query.cn;
        if (!cn) {
            d.result = d.app.channels;
            return;
        }
        const channels = d.app.channel(cn);
        const data = [];
        for (const { sid, Aip, user, _st } of channels.connections) {
            data.push({ sid, Aip, user, _st });
        }
        d.result = { cn, count: channels.length, data };
    },
    async send(d) {
        const get = d.params.query;
        const rs = await d.app.channel(get.cn).send(get.data);
        logger_1.default.log(rs);
        d.result = { ok: 1, num: d.app.channel(get.cn).length, rs };
    },
    async onlineUsers(d) {
        const sid = d.params.query.sid;
        const list = [];
        d.app.channel(`class/${sid}`).filter(({ role, user, sid: __sid }) => {
            if (!user)
                return;
            list.push({ _id: user._id, email: user.email, avatar: user.avatar, nickname: user.nickname, role, _sid: sid, __sid });
        });
        d.result = { ok: 1, list };
    },
    async clients(d) {
        const list = {};
        let count = 0;
        for (const cn of d.app.channels) {
            const channels = d.app.channel(cn);
            const data = [];
            for (const { Aip, user } of channels.connections) {
                data.push({ cn, Aip, nickname: user.nickname });
                count++;
            }
            list[cn] = data;
        }
        d.result = { count, list };
    },
};
exports.default = mod;
