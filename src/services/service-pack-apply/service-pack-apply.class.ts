import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')
export class ServicePackApply extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
  async extServicePack(one: any, params?: Params) {
    one.servicePackInfo = await this.app.service('service-pack').Model.findOne({_id: one.servicePack})
  }
  async extUser(one: any, params?: Params) {
    if (one.uid) {
      one.userInfo = await this.app.service('users').uidToInfo(one.uid)
    }
    if (one.follower && !one.schoolOfFollower) {
      one.followerInfo = await this.app.service('users').uidToInfo(one.follower)
    }
    if (one.follower && one.schoolOfFollower) {
      one.followerInfo = await this.app.service('school-user').Model.findOne({uid: one.follower, school: one.schoolOfFollower})
    }
  }
  async extSchool(one: any, params?: Params) {
    if (one.sharedSchool) {
      one.schoolInfo = await this.app.service('school-plan').Model.findOne({_id: one.sharedSchool})
    }
  }
  async extSchoolPrice(one: any, params?: Params) {
    if (one.sharedSchool) {
      one.schoolPriceInfo = await this.app.service('service-pack-school-price').Model.findOne({school: one.sharedSchool, servicePack: one.servicePack})
    }
  }
  async getCountType(query: any, params: Params) {
    return await this.Model.aggregate([
      {$match: {}},
      {
        $group: {
          _id: {
            mentoringType: '$mentoringType',
            status: '$status',
          },
          count: {$sum: 1},
        },
      },
    ])
  }
  async getCount({sharedSchool, status, servicePack, archive}: any, params: Params) {
    let query: any = {status, servicePack}
    if (archive !== undefined) {
      query.archive = archive
    }
    if (sharedSchool !== undefined) {
      query.sharedSchool = sharedSchool
    }
    let within = await this.Model.count({...query, withinSchool: true})
    let outside = await this.Model.count({...query, withinSchool: false})
    return {
      within,
      outside,
    }
  }
  async updateOrderInfo({uid, servicePack, sharedSchool, order}: any): Promise<any> {
    let serviceApplyData: any = await this.Model.findOne({uid, servicePack: servicePack, sharedSchool})
    if (serviceApplyData?.status == 1) {
      return await this.Model.updateOne({_id: serviceApplyData._id}, {$addToSet: {order}})
    }
  }
  async handleEmail(result: any, data: any, params: Params) {
    let {servicePack, sharedSchool} = result
    let {status, interviewInvited} = data

    // 通过邮件
    if (status == 1) {
      let servicePackData: any = await this.app.service('service-pack').Model.findOne({_id: servicePack})
      let servicePriceData: any = await this.app.service('service-pack-school-price').Model.findOne({school: sharedSchool, servicePack: servicePack})

      if ((servicePackData.salesTarget.indexOf('personal') > -1 && !sharedSchool) || (servicePriceData?.priceEnable && sharedSchool)) {
        // 有购买邮件
        this.send(result, 'ReminderOfApplicationApprovalWithOrder', params)
      } else {
        // 无购买邮件
        this.send(result, 'ReminderOfApplicationApprovalWithoutOrder', params)
      }
    }
    // 拒绝邮件
    if (status == -1) {
      this.send(result, 'ReminderOfApplicationRejection', params)
    }
    // 面试邮件
    if (interviewInvited) {
      this.send(result, 'ReminderOfInterview', params)
    }
  }
  async send(doc: any, tpl: string, params: Params): Promise<any> {
    const {uid, email, servicePack, sharedSchool} = doc
    let user = {
      _id: uid,
      email: email,
    }
    let userData = await this.app.service('users').uidToInfo(uid)
    let servicePackData: any = await this.app.service('service-pack').Model.findOne({_id: servicePack})

    let url = ''
    let url2 = ''
    if (tpl === 'ReminderOfInterview') {
      url = `${SiteUrl}/v2/service/pack/${servicePack}`
      if (sharedSchool) {
        url += `?sharedSchool=${sharedSchool}`
      }
    } else if (tpl === 'ReminderOfApplicationApprovalWithOrder') {
      url = `${SiteUrl}/v2/service/pack/${servicePack}`
      if (sharedSchool) {
        url += `?sharedSchool=${sharedSchool}`
      }
      url2 = `${SiteUrl}/v2/`
    } else if (tpl === 'ReminderOfApplicationApprovalWithoutOrder') {
      url = `${SiteUrl}/v2/service/pack/${servicePack}`
      if (sharedSchool) {
        url += `?sharedSchool=${sharedSchool}`
      }
    } else if (tpl === 'ReminderOfApplicationRejection') {
      url = `${SiteUrl}/v2/premcpack/enroll/${servicePack}`
      if (sharedSchool) {
        url += `?sharedSchool=${sharedSchool}`
      }
    }

    return await this.app.service('notice-tpl').mailto(tpl, user, {username: userData.name.join(' '), name: servicePackData.name, url, url2}, params.user?._id)
  }

  // 面试失败/过期 手动return
  async getInterviewReturn({id}: any): Promise<any> {
    let serviceApplyData: any = await this.Model.findOne({_id: id})
    let {interviewInvited, takeaway, takeawayId} = serviceApplyData
    // if (!interviewInvited) {
    //   return Promise.reject(new GeneralError('The status of the application can not be returned.'))
    // }
    // takeaway解绑
    let takeawayData: any = await this.app.service('session-takeaway-snapshot').Model.findOne({_id: takeawayId})
    let session: any = await this.app.service('session').Model.findOne({_id: takeawayData.session})
    if (session?.booking) {
      await this.app.service('service-booking').Model.updateOne(
        {_id: session.booking},
        {
          $unset: {session: 1, servicePackApply: 1},
        }
      )
    }
    if (session) {
      await this.app.service('session').remove(session._id)
    }
    return await this.Model.updateOne(
      {_id: id},
      {
        interviewStatus: 0,
        interviewInvited: false,
        interviewApply: false,
        $unset: {interviewOrder: '', takeaway: '', takeawayId: '', takeawayCreatedAt: '', interviewPurchaseExpireAt: ''},
      }
    )
  }
  async cron1({}: any, params?: Params): Promise<any> {
    this.autoReturnInterviewPurchaseExpire()
  }

  // 面试购买过期 自动return
  async autoReturnInterviewPurchaseExpire(): Promise<any> {
    this.Model.find({status: 0, interviewPurchaseExpireAt: {$lt: Date.now()}, interviewOrder: {$exists: false}}).then(async (rs: any) => {
      for (let i = 0; i < rs.length; i++) {
        const item = rs[i]
        await this.Model.updateOne(
          {_id: item._id},
          {
            interviewInvited: false,
            interviewPurchaseExpired: true,
            $unset: {interviewPurchaseExpireAt: ''},
          }
        )
      }
    })
  }

  async getGroupByFollower({}: any, params: Params): Promise<any> {
    let match: any = {
      follower: {$exists: true},
    }
    let list = await this.Model.aggregate([
      {$match: match},
      {
        $group: {
          _id: {follower: '$follower', schoolOfFollower: '$schoolOfFollower'},
          follower: {$first: '$follower'},
          schoolOfFollower: {$first: '$schoolOfFollower'},
          count: {$sum: 1},
        },
      },
      {$sort: {count: -1}},
    ])
    for (let i = 0; i < list.length; i++) {
      await this.extUser(list[i])
    }
    return list
  }

  // session结束后,获取takeaway,更新apply面试状态 #5538
  async updateTakeaway({session, snapshot}: any) {
    let {students} = snapshot
    for (let i = 0; i < students.length; i++) {
      const student = students[i]
      let booking: any = await this.app.service('service-booking').Model.findOne({'session._id': session, booker: student.uid})
      if (!booking) {
        continue
      }
      let takeaway: any = await this.app.service('session-takeaway-snapshot').Model.findOne({session, uid: student.uid})
      const takeawayUrl = `/account/takeaway/${takeaway?.session}/view/${student?.uid}`

      await this.Model.updateOne(
        {_id: booking.servicePackApply},
        {
          interviewStatus: 1,
          takeaway: takeawayUrl,
          takeawayId: takeaway?._id,
          takeawayCreatedAt: takeaway?.createdAt,
        }
      )
    }
  }

  // async getIndex({}: any, params: Params): Promise<any> {
  //   return await this.Model.collection.getIndexes()
  // }
  // async getDropIndex({name}: any, params: Params): Promise<any> {
  //   return await this.Model.collection.dropIndex(name)
  // }
}
