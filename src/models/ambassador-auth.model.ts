// ambassador-auth-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'ambassadorAuth'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      uid: {type: String, index: true, required: true},
      role: {type: String, enum: Agl.usersRoles}, // user.roles
      status: {type: Number, index: true, default: 1}, // 0: 未申请/Apply verification, 1:申请中/Under processing, 2: 通过/Verified, -1: 拒绝/Under processing
      reason: {type: String, trim: true},
      attachments: [
        // 附件
        {
          filename: {type: String, trim: true},
          mime: {type: String, trim: true},
          hash: {type: String, trim: true},
          date: {type: Date}, // 上传时间
          type: {type: String, trim: true}, // 认证类型, conf.val.attachmentType
          size: {type: Number}, // 文件大小
        },
      ],
      feedback: {
        // 留言反馈
        message: {type: String}, // 用户留言内容
        date: {type: Date}, // 留言时间
        read: {type: Boolean, default: false}, // read status
        reply: {type: String}, // 后台回复内容
        replyDate: {type: Date},
        replyRead: {type: Boolean, default: false}, // read status
      },
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
