"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClassApply = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
class ClassApply extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async extData(one, params) {
        // one.classInfo = await this.app.service('classes').Model.findOne({_id: one.class})
        one.studentInfo = await this.app.service('students').Model.findOne({ _id: one.student });
    }
    // 报名通过学生数量
    async getCount({ class: classId }) {
        return await this.Model.count({ class: classId, status: 1 });
    }
    // 到上限后拒绝所有报名,且邮件通知
    async checkLimit({ classId }) {
        var _a;
        let classData = await this.app.service('classes').Model.findOne({ _id: classId });
        let count = ((_a = classData === null || classData === void 0 ? void 0 : classData.count) === null || _a === void 0 ? void 0 : _a.student) || 0;
        // 到上限后拒绝所有报名,且邮件通知
        if (classData.maxParticipants && count >= classData.maxParticipants) {
            let pendingList = await this.Model.find({ class: classId, status: 0 });
            for (let i = 0; i < pendingList.length; i++) {
                const { _id } = pendingList[i];
                this.patch(_id, { status: -1 });
            }
        }
    }
    async send(doc, tpl, params) {
        var _a;
        let { class: classId, student, className } = doc;
        if (!className) {
            let classData = await this.app.service('classes').Model.findOne({ _id: classId });
            className = classData.name;
        }
        let studentData = await this.app.service('students').Model.findOne({ _id: student });
        let url = '';
        // if (tpl === 'StudentRejectedToBeAddedToASubjectClass') url = ``
        // if (tpl === 'StudentRemovedFromSubjectClass') url = ``
        if (tpl === 'StudentAddedToSubjectClass')
            url = `${SiteUrl}/v2/account/student/all?classId=${classId}`;
        return await this.app
            .service('notice-tpl')
            .mailto(tpl, studentData.email, { studentName: studentData.name.join(' '), className: className, url }, (_a = params.user) === null || _a === void 0 ? void 0 : _a._id);
    }
    // 清理报名记录
    async clearApply({ classId, student }) {
        await this.Model.deleteOne({ class: classId, student });
    }
}
exports.ClassApply = ClassApply;
