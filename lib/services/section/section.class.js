"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Section = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const errors_1 = require("@feathersjs/errors");
class Section extends feathers_mongoose_1.Service {
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    // Example: Fetch a section with custom projection
    async getDetails(_id, params) {
        const section = await this.Model.findById(_id);
        if (!section)
            throw new errors_1.NotFound('Section not found');
        return section;
    }
    // Example: Custom create logic
    async create(data, params) {
        // Custom pre-processing logic (e.g., validate inputs)
        return super.create(data, params);
    }
    // Example: Custom update logic
    async patch(_id, data, params) {
        if (data.comment) {
            return this.addUserInfoToComment(_id, data, params);
        }
        else if (data.message) {
            return this.addUserInfoToMessage(_id, data, params);
        }
        else if (data.file) {
            return this.addUserInfoToFile(_id, data, params);
        }
        else if (data.serviceProviderId) {
            return this.addUserInfoToProvider(_id, data, params);
        }
        else if (data.complain) {
            return this.addUserInfoToComplain(_id, data, params);
        }
        // Custom validation or post-update logic
        return super.patch(_id, data, params);
    }
    async addUserInfoToComment(id, data, params) {
        var _a, _b;
        const userId = ((_a = params.user) === null || _a === void 0 ? void 0 : _a._id) || ((_b = params.user) === null || _b === void 0 ? void 0 : _b.id);
        if (!userId)
            throw new Error('User not authenticated');
        const user = await this.app.service('users').get(userId);
        const newComment = {
            userId,
            name: user.name || [],
            nickname: user.nickname || '',
            avatar: user.avatar || '',
            comment: data.comment,
            createdAt: new Date(),
        };
        return await this.Model.findByIdAndUpdate(id, {
            $push: {
                comments: {
                    $each: [newComment],
                    $position: 0,
                },
            },
        }, { new: true });
    }
    async addUserInfoToMessage(id, data, params) {
        var _a, _b;
        const userId = ((_a = params.user) === null || _a === void 0 ? void 0 : _a._id) || ((_b = params.user) === null || _b === void 0 ? void 0 : _b.id);
        if (!userId)
            throw new Error('User not authenticated');
        const user = await this.app.service('users').get(userId);
        const newMessage = {
            userId,
            name: user.name || [],
            nickname: user.nickname || '',
            avatar: user.avatar || '',
            message: data.message,
            createdAt: new Date(),
        };
        return await this.Model.findByIdAndUpdate(id, { $push: { messages: newMessage } }, { new: true });
    }
    async addUserInfoToFile(id, data, params) {
        var _a, _b;
        const userId = ((_a = params.user) === null || _a === void 0 ? void 0 : _a._id) || ((_b = params.user) === null || _b === void 0 ? void 0 : _b.id);
        if (!userId)
            throw new Error('User not authenticated');
        const user = await this.app.service('users').get(userId);
        const newFile = {
            userId,
            name: user.name || [],
            nickname: user.nickname || '',
            avatar: user.avatar || '',
            cover: data.file,
            isComplain: data.isComplain,
            uploadedTime: new Date(),
        };
        return await this.Model.findByIdAndUpdate(id, {
            $push: {
                files: {
                    $each: [newFile],
                    $position: 0,
                },
            },
        }, { new: true });
    }
    async addUserInfoToProvider(id, data, params) {
        var _a, _b;
        const userId = ((_a = params.user) === null || _a === void 0 ? void 0 : _a._id) || ((_b = params.user) === null || _b === void 0 ? void 0 : _b.id);
        if (!userId)
            throw new Error('User not authenticated');
        const user = await this.app.service('users').get(data.serviceProviderId);
        const newProvider = {
            userId: data.serviceProviderId,
            name: user.name || [],
            nickname: user.nickname || '',
            avatar: user.avatar || '',
            assignedTime: new Date(),
        };
        return await this.Model.findByIdAndUpdate(id, { $push: { serviceProviderDetails: newProvider } }, { new: true });
    }
    async addUserInfoToComplain(id, data, params) {
        var _a, _b;
        const userId = ((_a = params.user) === null || _a === void 0 ? void 0 : _a._id) || ((_b = params.user) === null || _b === void 0 ? void 0 : _b.id);
        if (!userId)
            throw new Error('User not authenticated');
        const user = await this.app.service('users').get(userId);
        const newComplain = {
            userId,
            name: user.name || [],
            nickname: user.nickname || '',
            avatar: user.avatar || '',
            reason: data.complain,
            isResolved: false,
            createdAt: new Date(),
            uploadedFiles: data.files,
        };
        return await this.Model.findByIdAndUpdate(id, { $push: { complains: newComplain }, $set: { complainStatus: data.complainStatus } }, { new: true });
    }
}
exports.Section = Section;
