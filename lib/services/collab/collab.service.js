"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const collab_class_1 = require("./collab.class");
const collab_model_1 = __importDefault(require("../../models/collab.model"));
const collab_hooks_1 = __importDefault(require("./collab.hooks"));
function default_1(app) {
    const options = {
        Model: (0, collab_model_1.default)(app),
        paginate: app.get('paginate')
    };
    // Initialize our service with any options it requires
    app.use('/collab', new collab_class_1.Collab(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('collab');
    service.hooks(collab_hooks_1.default);
}
exports.default = default_1;
