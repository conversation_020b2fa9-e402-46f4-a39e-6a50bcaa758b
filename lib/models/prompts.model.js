"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'prompts';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, required: true },
        unit: { type: String, required: true },
        curriculum: { type: String, required: true },
        service: {
            type: { type: [String] },
            participants: { type: String, sparse: true, enum: Agl.subjectsParticipants }, // participants
        },
        subjects: [
            {
                label: { type: String, sparse: true, trim: true },
                value: { type: String, sparse: true, trim: true },
            },
        ],
        grades: [
            {
                label: { type: String, sparse: true, trim: true },
                value: { type: String, sparse: true, trim: true },
            },
        ],
        publish: { type: Boolean, default: false },
        sales: { type: Number, default: 0 },
        used: { type: Number, default: 0 },
        pages: { type: Schema.Types.Mixed },
        questions: { type: Schema.Types.Mixed },
        materials: { type: Schema.Types.Mixed }, // materials snapshot
    }, {
        timestamps: true,
    });
    /*
    db["prompts"].createIndex(
      {
        'questions.outlines.outline.name': 'text',
        'questions.outlines.outline.child.name': 'text',
        'questions.outlines.outline.child.child.name': 'text',
        'questions.outlines.outline.child.child.child.name': 'text',
      },
      {name: 'name', sparse: true}
    )
    */
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
