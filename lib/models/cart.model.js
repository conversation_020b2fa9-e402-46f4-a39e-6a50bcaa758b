"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'cart';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        buyer: { type: String, required: true },
        goodsId: { type: String, required: true },
        style: { type: String, enum: ['unit', 'session'] },
        goods: { type: Object },
        inviter: { type: String, trim: true },
        schoolInviter: { type: String, trim: true },
        inviteSource: { type: String, trim: true, enum: ['new_prompt', 'sales_follow_up'] },
        inviteSourceId: { type: String, trim: true }, //目前只有一个场景,从new prompt下分享的workshop的_id
    }, {
        timestamps: false,
    });
    schema.index({ buyer: 1, goodsId: 1 }, { unique: false });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
