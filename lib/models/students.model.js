"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'students';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, trim: true },
        id: { type: Number, sparse: true },
        school: { type: String, required: true, trim: true },
        email: { type: String, sparse: true, trim: true },
        mobile: { type: String, trim: true, sparse: true },
        name: { type: [String], trim: true },
        nickname: { type: String, trim: true },
        avatar: { type: String, trim: true },
        dob: { type: String, sparse: true, trim: true },
        gender: { type: String, enum: Agl.gender, sparse: true },
        class: { type: [String], trim: true },
        subjectClass: { type: [String], trim: true },
        status: { type: Number, default: 0 },
        parent: {
            email: { type: String, sparse: true, trim: true },
            name: { type: [String], sparse: true, trim: true },
            phone: { type: String, sparse: true, trim: true },
            status: { type: Number, default: 0 }, // 0: inactive, 1: pending, 2: success
        },
        password: { type: String, trim: true },
        del: { type: Boolean, default: false }, // del
    }, {
        timestamps: true,
    });
    schema.index({ school: 1, email: -1 }, { sparse: true });
    // schema.index({school: 1, 'name.0': 1, 'name.1': 1, 'parent.email': -1}, {unique: true, sparse: true})
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
