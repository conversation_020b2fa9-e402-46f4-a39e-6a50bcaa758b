"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'response';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        sid: { type: String, index: true, required: true },
        uid: { type: String, index: true, required: true },
        nickname: { type: String, required: true },
        page: { type: String, required: true },
        type: { type: String, enum: Agl.questionsTypes },
        content: { type: String },
        answer: { type: [String], sparse: true },
        locked: { type: Boolean, sparse: true },
        point: { type: Schema.Types.Mixed },
        json: { type: Schema.Types.Mixed },
        quizId: { type: String }, //quizid for video
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
