"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Classes = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
class Classes extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async count({ school }, params) {
        const count = await this.Model.count({ school, del: false });
        await this.app.service('school-plan').Model.updateOne({ _id: school }, { 'count.class': count });
    }
    // auto create personal class
    async autoCreatePersonalClass(result, params) {
        var _a, _b, _c, _d;
        if ((_b = (_a = params.user) === null || _a === void 0 ? void 0 : _a.roles) === null || _b === void 0 ? void 0 : _b.includes('student'))
            return;
        const { data } = result;
        if (data.length > 0)
            return;
        const query = (_c = params.query) !== null && _c !== void 0 ? _c : {};
        const user = (_d = params.user) !== null && _d !== void 0 ? _d : {};
        if (query.school !== user._id)
            return;
        const grades = await this.app.service('conf-school').getGrades({}, params);
        const doc = await this.create({ school: user._id, grade: grades[0]._id, name: 'Personal' });
        data.push(doc);
    }
    async getStats({ grades }, params) {
        return await this.Model.aggregate([{ $match: { del: false, grade: { $in: grades } } }, { $group: { _id: '$grade', count: { $sum: 1 } } }]);
    }
    async studentCount(classIds, school) { }
    async teacherCount(classIds) {
        const arr = await this.app
            .service('school-user')
            .Model.find({ $or: [{ class: { $in: classIds } }, { head: { $in: classIds } }] })
            .select(['class', 'head']);
        const list = {}, rs = {};
        arr.map((v) => {
            for (const c of v.head) {
                if (!list[c])
                    list[c] = {};
                list[c][v._id] = 1;
            }
            for (const c of v.class) {
                if (!list[c])
                    list[c] = {};
                list[c][v._id] = 1;
            }
        });
        for (const id in list)
            rs[id] = Object.keys(list[id]).length;
        return rs;
    }
    async getGradesCount({ school }, params) {
        var _a;
        school = school || ((_a = params.user) === null || _a === void 0 ? void 0 : _a._id);
        if (!school)
            return {};
        const arr = await this.Model.find({ type: 'standard', school }).select(['grade']);
        const rs = {};
        for (const o of arr) {
            if (!o.grade)
                continue;
            if (!rs[o.grade])
                rs[o.grade] = 1;
            else
                rs[o.grade]++;
        }
        return rs;
    }
    // 学科班课可报名列表
    async getSubjectClassApplyList({ classId, school }, params) {
        var _a, _b;
        const user = (_a = params.user) !== null && _a !== void 0 ? _a : {};
        let studentData = await this.app.service('students').Model.findOne({ school, uid: user._id });
        let classFilter = [];
        let subjectFilter = [];
        let query = {
            type: 'subject',
            'enroll.enable': true,
            'enroll.classes': classId,
            deadline: { $gt: Date.now() },
        };
        if ((_b = studentData.subjectClass) === null || _b === void 0 ? void 0 : _b.length) {
            classFilter = studentData.subjectClass;
            let classData = await this.Model.find({ _id: { $in: classFilter } });
            subjectFilter = classData.filter((v) => v.subject && !v.approvalEnable).map((v) => v.subject);
            if (subjectFilter.length > 0) {
                query.$or = [
                    { approvalEnable: true },
                    {
                        $or: [
                            { subject: { $nin: subjectFilter }, approvalEnable: false },
                            { subjectTemporary: { $exists: true }, approvalEnable: false },
                        ],
                    },
                ];
            }
            query._id = { $nin: studentData.subjectClass };
        }
        const list = await this.Model.find(query).lean();
        let curriculumIds = list.map((v) => v.curriculum).filter((v) => v);
        let subjectIds = list.map((v) => v.subject).filter((v) => v);
        let curriculumData = await this.app.service('curric').Model.find({ _id: { $in: curriculumIds } });
        let subjectData = await this.app.service('subjects').Model.find({ _id: { $in: subjectIds } });
        let curriculumDict = curriculumData.reduce((acc, v) => {
            acc[v._id] = v;
            return acc;
        }, {});
        let subjectDict = subjectData.reduce((acc, v) => {
            acc[v._id] = v;
            return acc;
        }, {});
        // 按大纲学科分类
        let dict = {};
        for (const o of list) {
            if (!dict[o.curriculum]) {
                dict[o.curriculum] = {};
            }
            if (o.subject) {
                if (!dict[o.curriculum][o.subject]) {
                    dict[o.curriculum][o.subject] = [];
                }
                dict[o.curriculum][o.subject].push(o);
            }
            else {
                if (!dict[o.curriculum][o.subjectTemporary]) {
                    dict[o.curriculum][o.subjectTemporary] = [];
                }
                dict[o.curriculum][o.subjectTemporary].push(o);
            }
        }
        // 转化数组 拼接大纲学科详情
        let formatList = [];
        for (const curriculum in dict) {
            formatList.push({
                curriculum: curriculumDict[curriculum],
                subjectList: [],
            });
            for (const subject in dict[curriculum]) {
                let list = dict[curriculum][subject];
                formatList[formatList.length - 1].subjectList.push({
                    subject: subjectDict[subject] || { name: subject },
                    classList: list.sort((a, b) => a.createdAt - b.createdAt),
                });
            }
        }
        // 排序
        formatList.sort((a, b) => a.curriculum.name.localeCompare(b.curriculum.name));
        formatList.forEach((v) => {
            v.subjectList.sort((a, b) => a.subject.name.localeCompare(b.subject.name));
        });
        return {
            list: formatList,
        };
    }
}
exports.Classes = Classes;
