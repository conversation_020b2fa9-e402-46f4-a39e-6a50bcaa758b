"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'classes';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        school: { type: String, required: true },
        type: { type: String, default: 'standard', enum: Agl.classesTypes },
        host: { type: String },
        grade: { type: String },
        name: { type: String, required: true },
        del: { type: Boolean, default: false },
        count: {
            teacher: { type: Number, default: 0 },
            student: { type: Number, default: 0 },
            journalPub: { type: Number, default: 0 },
            journalUnPub: { type: Number, default: 0 },
        },
        // for subject
        term: { type: String, trim: true },
        curriculum: { type: String, trim: true },
        subject: { type: String, trim: true },
        subjectTemporary: { type: String, trim: true },
        enroll: {
            enable: { type: Boolean, default: false },
            classes: { type: [String], default: false }, // classes._id, filter(type === 'standard')
        },
        attachmentsCover: {
            filename: { type: String, trim: true },
            mime: { type: String, trim: true },
            hash: { type: String, trim: true }, // 文件SHA1, files._id
        },
        deadline: { type: Date },
        maxParticipants: { type: Number },
        approvalEnable: { type: Boolean, default: false },
        questions: { type: [String] },
        // time: {type: Number}, // session time, unit sec
        // break: {type: Number}, // break time, unit sec
        block: [
            {
                week: { type: Number },
                start: { type: String, required: true },
                end: { type: String, required: true },
                // every: {type: Number, default: 1},// 弃用,改为整体设置
                // repeat: {type: String, enum: Agl.classesBlockRepeat, default: 'week'},// 弃用,改为整体设置
            },
        ],
        every: { type: Number, default: 1 },
        repeat: { type: String, enum: Agl.classesBlockRepeat, default: 'week' },
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
