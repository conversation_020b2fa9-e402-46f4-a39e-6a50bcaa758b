"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const class_question_logs_class_1 = require("./class-question-logs.class");
const class_question_logs_model_1 = __importDefault(require("../../models/class-question-logs.model"));
const class_question_logs_hooks_1 = __importDefault(require("./class-question-logs.hooks"));
function default_1(app) {
    const options = {
        Model: (0, class_question_logs_model_1.default)(app),
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/class-question-logs', new class_question_logs_class_1.ClassQuestionLogs(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('class-question-logs');
    service.hooks(class_question_logs_hooks_1.default);
}
exports.default = default_1;
