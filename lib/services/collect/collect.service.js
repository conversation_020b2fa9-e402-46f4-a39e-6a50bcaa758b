"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const collect_class_1 = require("./collect.class");
const collect_model_1 = __importDefault(require("../../models/collect.model"));
const collect_hooks_1 = __importDefault(require("./collect.hooks"));
function default_1(app) {
    const options = {
        Model: (0, collect_model_1.default)(app),
        whitelist: ['$regex', '$options', '$search'],
        paginate: app.get('paginate'),
    };
    // Initialize our service with any options it requires
    app.use('/collect', new collect_class_1.Collect(options, app));
    // Get our initialized service so that we can register hooks
    const service = app.service('collect');
    service.hooks(collect_hooks_1.default);
}
exports.default = default_1;
