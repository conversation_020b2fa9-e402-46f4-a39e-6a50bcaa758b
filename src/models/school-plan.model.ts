// school-plan-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'schoolPlan'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      name: {type: String, index: true, trim: true},
      logo: {type: String, trim: true},
      country: {type: String, index: true, trim: true},
      city: {type: String, index: true, trim: true},
      address: {type: String, trim: true},
      phone: {type: [String], trim: true},
      start: {type: Date, index: true},
      end: {type: Date, index: true},
      status: {type: Number, index: true, default: 0}, // 0: applied, create, unpaid, 1: trial, 2: Paid, -1: Expired, 3: Created
      teacher: {type: Number, default: 1},
      student: {type: Number, default: 10},
      count: {
        teacher: {type: Number, default: 0},
        student: {type: Number, default: 0},
        grade: {type: Number, default: 0},
        class: {type: Number, default: 0},
      },
      space: {type: Number, default: 1000 * 1024 * 1024},
      pilot: {type: Boolean, default: false},
      personal: {type: Boolean, default: false},
      commissionEnable: {type: Boolean, default: false}, //佣金开关
      contact: {type: String}, // users._id 联系人 main contact
      inviter: {type: String, trim: true}, //邀请我的人
      pipelineEnable: {type: Boolean, default: false}, // 销售渠道 系统开关
      pipelineStatus: {type: Number, default: 0}, // 销售渠道 0: inactive, 1: pending, 2: success
      pipelineAt: {type: Date}, //同意时间
      contentProviderEnable: {type: Boolean, default: false}, // 内容供应商 系统开关
      contentProviderStatus: {type: Number, default: 0}, // 内容供应商 0: inactive, 1: pending, 2: success, 3: apply
      contentProviderAt: {type: Date}, //同意时间
      attachmentsLogo: {
        filename: {type: String, trim: true}, // 文件名
        mime: {type: String, trim: true}, // 文件 MIME
        hash: {type: String, trim: true}, // 文件SHA1, files._id
      },
      attachmentsCurriculum: [
        {
          type: {type: String, trim: true, enum: ['Certificate', 'Foundation', 'Master', 'Bachelor', 'Diploma']},
          subject: {type: Schema.Types.Mixed}, // subjects snapshot
          attachments: {
            filename: {type: String, trim: true},
            mime: {type: String, trim: true},
            hash: {type: String, trim: true},
            date: {type: Date}, // 上传时间
            type: {type: String, trim: true}, // 认证类型, conf.val.attachmentType
            size: {type: Number}, // 文件大小
          },
        },
      ],
      feedback: {
        // 留言反馈
        message: {type: String}, // 用户留言内容
        date: {type: Date}, // 留言时间
        read: {type: Boolean, default: false}, // read status
        reply: {type: String}, // 后台回复内容
        replyDate: {type: Date},
        replyRead: {type: Boolean, default: false}, // read status
      },
      balance: {type: Number, default: 0}, // 佣金+收入 美分
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
