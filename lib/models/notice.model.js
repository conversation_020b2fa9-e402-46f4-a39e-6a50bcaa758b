"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'notice';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        type: { type: String, required: true, trim: true, enum: ['mail', 'sms', 'inbox'] },
        ip: { type: String, trim: true },
        status: { type: Boolean, default: false },
        from: { type: String, trim: true },
        to: { type: String, trim: true },
        email: { type: String, trim: true },
        school: { type: String, trim: true },
        code: { type: String },
        conf: [{ type: String }],
        rs: { type: Schema.Types.Mixed },
        data: { type: Schema.Types.Mixed, required: true },
        smsProvider: { type: String, trim: true, enum: ['aws', 'tencent', 'plivo'] }, // 短信服务商
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
