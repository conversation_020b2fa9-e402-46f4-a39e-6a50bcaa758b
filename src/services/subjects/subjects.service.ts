// Initializes the `subjects` service on path `/subjects`
import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {Subjects} from './subjects.class'
import createModel from '../../models/subjects.model'
import hooks from './subjects.hooks'
import logger from '../../logger'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    subjects: Subjects & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    whitelist: ['$exists', '$regex', '$options', '$search'],
    paginate: app.get('paginate'),
  }

  // Initialize our service with any options it requires
  app.use('/subjects', new Subjects(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('subjects')

  service.hooks(hooks)
  setTimeout(async () => {
    const arr: any = await service.Model.find({uid: '1', curriculum: 'pd'}).select('subjectCode name participants')
    const pdSubjectCodeMap: any = {}
    const pdSubjectId2Code: any = {}
    for (const o of arr) {
      pdSubjectCodeMap[o.subjectCode] = o
      pdSubjectId2Code[o._id] = o.subjectCode
    }
    logger.info('load pd subjects ok:', Object.keys(pdSubjectCodeMap).join(', '))
    app.set('pdSubjectCodeMap', pdSubjectCodeMap)
    app.set('pdSubjectId2Code', pdSubjectId2Code)
  }, 1000)
  // 加载Steam topic第一层数据作为 subject匹配
  // service.Model.find({uid: '1', isLib: true, subjectCode: 'steam'})
  //   .select(['_id'])
  //   .then(async (arr: any) => {
  //     if (Acan.isEmpty(arr)) return
  //     const {snapshot}: any = await service.Model.findById(arr[0]._id).select(['snapshot.topic'])
  //     const subjectMap: any = {}
  //     for (const o of snapshot.topic) {
  //       subjectMap[o._id] = o.name
  //     }
  //     logger.info(subjectMap, 111)
  //   })
}
