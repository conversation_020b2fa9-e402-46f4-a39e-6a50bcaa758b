"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'session';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        sid: { type: String, unique: true, default: () => 'C00' + Acan.base62encode(Date.now() % 10000000000) },
        image: { type: String, trim: true },
        name: { type: String, trim: true },
        start: { type: Date, index: true, default: Date.now },
        end: { type: Date },
        ended: { type: Date },
        status: { type: String, index: true, default: 'live', enum: Agl.sessionStatus },
        type: { type: String, index: true, default: 'session', enum: Agl.sessionType },
        uid: { type: String, index: true },
        taught: { type: [String], index: true },
        zoom: { type: Schema.Types.Mixed },
        cid: { type: String },
        del: { type: Boolean, index: true, default: false },
        category: { type: String, trim: true },
        color: { type: String, trim: true },
        pid: { type: String, sparse: true, trim: true },
        parent: {
            // parent
            mode: { type: String, sparse: true, trim: true },
            group: { type: String, sparse: true, trim: true },
            groupName: { type: String, sparse: true, trim: true }, // unit.linkGroup.name
        },
        unitType: { type: String, sparse: true, trim: true },
        sessionType: { type: String, enum: Agl.unitSessionType },
        // subjects: {type: [String], trim: true}, // subjects
        subjects: [
            {
                // publish subjects
                label: { type: String, sparse: true, trim: true },
                value: { type: String, sparse: true, trim: true },
            },
        ],
        // extend
        size: { type: Number, default: 0 },
        id: { type: String },
        rev: { type: String },
        overview: { type: String, trim: true },
        // for classroom control
        block: { type: Boolean, default: false },
        guest: { type: Boolean, default: false },
        welcome: { type: Boolean, default: true },
        countdown: {
            type: { type: Number, default: 0 },
            studentCtrl: { type: Boolean, default: false },
            deadline: { type: Date },
            down: { type: Number }, // down min
        },
        // for workshop
        // price: {type: Number, default: 0}, // free: 0, price $1 = 100
        discount: {
            val: { type: Number, default: 0 },
            price: { type: Number, default: 0 },
            end: { type: Date },
            size: { type: Number, default: 1 }, // group size
        },
        regDate: { type: Date },
        regMax: { type: Number, default: 100 },
        regNum: { type: Number, index: true, default: 0 },
        // 公开课
        reg: [
            {
                _id: { type: String },
                avatar: { type: String },
                nickname: { type: String, trim: true },
                last: { type: Date, default: Date.now },
                order: { type: String }, // order._id
            },
        ],
        // for class session
        school: { type: String, sparse: true },
        classId: { type: String, sparse: true },
        className: { type: String, sparse: true },
        taskType: { type: String, sparse: true },
        unit: { type: String },
        students: { type: [String] },
        grades: { type: [String] },
        // for unit session, courses
        childSize: { type: Number, default: 0 },
        childs: [
            {
                // _id: {type: String} , // session._id
                cid: { type: String },
                group: { type: String },
                groupName: { type: String },
                mode: { type: String, required: true },
                sid: { type: String },
                sessionType: { type: String, enum: Agl.unitSessionType }, // unit.sessionType
            },
        ],
        // snapshot data
        task: { type: Schema.Types.Mixed },
        video: { type: Schema.Types.Mixed },
        questions: { type: Schema.Types.Mixed },
        materials: { type: Schema.Types.Mixed },
        toolStat: { type: Schema.Types.Mixed },
        reminder: { type: Number, default: 0 },
        drawkey: { type: String },
        // for service 捆绑服务包（只有父课程能捆绑，pid: null）
        premium: { type: Boolean, default: false },
        promotion: { type: Boolean, default: false },
        promotionSession: { type: String },
        servicePack: {
            // 公开课捆绑销售的服务包
            _id: { type: String },
            times: { type: Number },
            price: { type: Number }, // 美分，服务包价格 = service-pack.discount 折扣 * live直播课的数量
        },
        booking: { type: String, sparse: true },
        tutorialPackUser: { type: String },
        isAutoCreate: { type: Boolean, default: false },
        income: { type: Number, default: 0 },
        personal: { type: Boolean, default: false },
        count: {
            students: { type: Number, default: 0 },
            report: { type: Number, default: 0 }, // 已经发送报告的数量
        },
        isView: { type: Boolean, default: false },
        freePromptCount: { type: Number, default: 0 },
        substituteWithin: { type: Boolean, default: false },
        substituteTeacher: { type: String },
        substituteTeacherStatus: { type: Number },
        substituteTeacherMessage: { type: String },
        substituteAdmin: { type: String },
        substituteAdminStatus: { type: Number },
        substituteAdminMessage: { type: String },
        substitutePackUser: { type: String },
        substituteServicePackSnapshot: { type: Schema.Types.Mixed },
        substituteServicePackUserSnapshot: { type: Schema.Types.Mixed },
        substituteSubject: { type: [String], trim: true },
        substituteTopic: { type: [String], trim: true },
        substitutePush: { type: [String], trim: true },
        substituteDuration: { type: Number },
        substitutePushTime: { type: Date },
        substitutePushAll: { type: Boolean },
        substitutePriorityPush: { type: Boolean },
        substituteReminder: { type: Boolean },
        substituteMatched: { type: Boolean },
        substituteExclude: { type: [String], trim: true },
        substituteNoCompensation: { type: Boolean },
        substituteOperateAt: { type: Date },
        order: { type: String }, // order._id 目前只有以下一个场景使用:remove session时,重置premium_cloud为未使用
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
