"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'statsTarget';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        _id: { type: String, required: true },
        // desc: { type: String }, // 大纲内容
        bloom: { type: [Number], default: [0, 0, 0, 0, 0, 0] },
        knowledge: { type: [Number], default: [0, 0, 0, 0] },
        terms: [{ type: String, trim: true }],
        tags: [{ type: String, trim: true }], // 最后使用的 tagst
    }, {
        timestamps: true
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
