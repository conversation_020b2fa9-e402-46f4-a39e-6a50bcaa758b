"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
// Don't remove this comment. It's needed to format import lines nicely.
const hook_1 = __importDefault(require("../../hook"));
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [],
        get: [hook_1.default.toClass],
        create: [
            async (d) => {
                let { at, class: classId } = d.data;
                if (!at || at == 'all') {
                    let students = await d.app.service('students').Model.find({ class: classId });
                    d.data.to = students.map((s) => s.uid).filter((s) => s);
                }
            },
        ],
        update: [],
        patch: [],
        remove: [],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                if (d.result) {
                    for (let i = 0; i < d.result.data.length; i++) {
                        await d.service.extUser(d.result.data[i]);
                    }
                }
            },
        ],
        get: [
            async (d) => {
                if (hook_1.default.classExist(d))
                    return d;
                await d.service.extUser(d.result);
            },
        ],
        create: [
            async (d) => {
                var _a;
                let { class: classId, to, message, school } = d.data;
                let classData = await d.app.service('classes').Model.findOne({ _id: classId });
                let students = await d.app.service('students').Model.find({ class: classId, uid: { $in: to } });
                let schoolPlan = await d.app.service('school-plan').Model.findOne({ _id: school });
                for (let i = 0; i < students.length; i++) {
                    const student = students[i];
                    let user = await d.app.service('users').uidToInfo(student.uid);
                    let url = `${SiteUrl}/v2/study/announcement?classId=${classId}&schoolId=${school}`;
                    d.app.service('notice-tpl').mailto('MessageToAllRegardingClassAnnouncement', user, {
                        username: student.name.join(' '),
                        class: classData.name,
                        school: schoolPlan.name,
                        message,
                        url,
                    }, (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id);
                }
            },
        ],
        update: [],
        patch: [],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
