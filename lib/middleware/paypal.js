"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    app.post('/paypal/completed', async (req, res) => {
        const { event_type, resource } = req.body;
        if (event_type === 'PAYMENT.CAPTURE.COMPLETED') {
            await app.service('paypal-webhook').Model.create({ body: req.body });
            const orderId = resource.invoice_id;
            if (!orderId) {
                return res.json({
                    code: 200,
                });
            }
            let order = await app.service('order').Model.findOne({ _id: orderId });
            if (order.status === 100 && !order.settled) {
                let paymentInfo = {
                    paymentInstrumentType: 'paypal_account',
                    cardType: '',
                    last4: '',
                };
                await app.service('order').patch(orderId, { paypalId: resource.id, settled: true, $push: { payMethod: 'paypal' }, paymentInfo, paidAt: new Date() });
                await app.service('order').completeOrder(order);
            }
        }
        res.json({
            code: 200,
        });
    });
}
exports.default = default_1;
