"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'serviceBooking';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        packUser: { type: String, required: true },
        packUserData: { type: [String] },
        packUserTasks: { type: [String] },
        booker: { type: String, required: true },
        servicer: { type: String, required: true },
        type: { type: String, required: true, enum: Agl.ServiceType },
        mentoringType: { type: String, enum: Agl.MentoringType },
        oldSession: {
            // 历史课程
            _id: { type: String },
            name: { type: String },
            image: { type: String, trim: true },
        },
        session: {
            // 服务人新排的课程
            _id: { type: String },
            name: { type: String },
            image: { type: String, trim: true },
            status: { type: String },
        },
        carer: {
            subject: { type: String },
            hasView: { type: Boolean, default: false },
            used: { type: Boolean, default: false }, // 是否已经用过管家服务
        },
        start: { type: Date },
        end: { type: Date },
        duration: { type: Number },
        times: { type: Number, default: 1 },
        // 预约的留言变化：https://github.com/zran-nz/bug/issues/5123
        message: { type: String },
        slides: {
            packUserTask: { type: String },
            pages: { type: Schema.Types.Mixed }, // 学校所选的ppt对象，公开课捆绑服务包取课堂快照 用oldSession._id 查询session
        },
        // 留言附件，老师import的时候需要插入到课堂
        attachments: [
            {
                filename: { type: String, trim: true },
                mime: { type: String, trim: true },
                hash: { type: String, trim: true }, // 文件SHA1, files._id
            },
        ],
        cancel: { type: String, sparse: true, enum: ['servicer', 'booker', 'timeout'] },
        canceledAt: { type: Date },
        reminder: { type: Number, default: 0 },
        reminderBooked: { type: Number, default: 0 },
        accident: {
            id: { type: String },
            status: { type: String, enum: ['pending', 'approved', 'rejected'] },
            tags: { type: [String] }, // 标签
        },
        servicePackApply: { type: String },
        serviceAuthId: { type: String },
        topic: { type: [String] }, // 匹配老师时选择的 topic service-pack-user.snapshot.topic
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
