import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks
import hook from '../../hook'

export default {
  before: {
    all: [],
    find: [
      authenticate('jwt'),
      hook.queryDate('updatedAt'),
      hook.selectList,
      async (d: HookContext) => {
        hook.sysQuery('uid', true, d.service.allowRoles)(d)
        // const query = d.params.query ?? {}
        // if (query.importUsers) {
        //   delete query.uid
        // }
      },
    ],
    get: [hook.toClass],
    create: [
      authenticate('jwt'),
      (d: HookContext) => {
        d.data.uid = d.params.user?._id
        if (d.data.status === 1) {
          d.data['approval.submitted'] = new Date() // 提交时间
        }
      },
    ],
    update: [authenticate('jwt')],
    patch: [authenticate('jwt')],
    remove: [authenticate('jwt')],
  },

  after: {
    all: [],
    find: [
      async (d: HookContext) => {
        if (isDev) d.result.query = d.params.query
        for (const o of d.result.data) {
          d.service.extPagesLimit(o, d.params)
          await d.service.ext(o)
          await d.service.extFollower(o)
        }
      },
    ],
    get: [
      async (d: HookContext) => {
        if (hook.classExist(d)) return d
        d.service.extPagesLimit(d.result, d.params)
        await d.service.ext(d.result)
        await d.service.extFollower(d.result)
      },
    ],
    create: [
      // async (d: HookContext) => {
      //   let {subject} = d.result
      //   if (subject) {
      //     d.service.send(d.result, 'VerificationStatusChanged', d.params)
      //   }
      // },
    ],
    update: [],
    patch: [
      async (d: HookContext) => {
        // 精品课件认证审核后更新 unit.premiumAuth
        const {unit, inviter, serviceRoles} = d.result
        const {interviewInvited} = d.data
        if ([2, -1].includes(d.data.status) && unit?._id) {
          await d.app.service('unit').Model.updateOne({_id: unit._id}, {premiumAuth: d.data.status === 2})
        }
        if (d.data.status === 2) {
          // 更新最新的认证时间
          await d.app.service('service-conf').Model.updateOne({_id: d.result.uid}, {$set: {lastAuth: new Date()}})

          if (unit?._id) {
            // 精品认证课件
            d.app.service('service-auth').send(d.result, 'Approval/RejectionOfPremiumContent', d.params)
          } else {
            d.app.service('service-auth').send(d.result, 'verificationapproved', d.params)
          }
          if (inviter) {
            d.app.service('service-auth').authVerifyPoint(d.result)
          }
          if (
            serviceRoles.includes('mentoring') ||
            serviceRoles.includes('substitute') ||
            serviceRoles.includes('consultant') ||
            serviceRoles.includes('onCampus')
          ) {
            let user = await d.app.service('users').Model.findOne({_id: d.result.uid})
            if (!user.serviceCalendarEmail) {
              d.app.service('service-auth').send(d.result, 'SetTheServiceAvailabilityUnderTheCalendar', d.params)
              await d.app.service('users').Model.updateOne({_id: d.result.uid}, {$set: {serviceCalendarEmail: true}})
            }
          }
        }
        if (d.data.status === -1) {
          if (unit?._id) {
            d.app.service('service-auth').send(d.result, 'Approval/RejectionOfPremiumContent', d.params)
          } else {
            d.app.service('service-auth').send(d.result, 'verificationrejected', d.params)
          }
        }
        // 需要下架关联服务包和提示 https://github.com/zran-nz/bug/issues/5136
        if (d.data.status === 0) {
          const packList = await d.app.service('service-pack').Model.find({'contentOrientated.premium': d.id}).select(['contentOrientated'])
          for (const o of packList) {
            for (const co of o.contentOrientated) {
              if (co.premium !== d.id) continue
              co.message = `${unit.name} has been withdrawn by the publisher.`
            }
            await d.app.service('service-pack').Model.updateOne({_id: o._id}, {$set: {status: false, contentOrientated: o.contentOrientated}})
          }
        }
        // 面试邀请邮件
        if (interviewInvited) {
          d.app.service('service-auth').send(d.result, 'ReminderOfInterview(Teachers)', d.params)
        }
      },
    ],
    remove: [
      async (d: HookContext) => {
        // 下架关联服务包，并设置未完善
        if (d.result.unit?._id) await d.app.service('service-pack').upByAuth(d.result)
        let {subject} = d.result
        if (subject) {
          d.service.send(d.result, 'VerificationStatusChanged', d.params)
        }
      },
    ],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
