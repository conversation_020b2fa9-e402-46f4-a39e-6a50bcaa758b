"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Collab = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
const bson_1 = require("bson");
const logger_1 = __importDefault(require("../../logger"));
const roleMap = { read: 'reader', write: 'writer', comment: 'commenter', owner: 'owner' };
class Collab extends feathers_mongoose_1.Service {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async isMember({ rid }, params) {
        var _a;
        return await this.Model.findOne({ rid, 'members.email': (_a = params.user) === null || _a === void 0 ? void 0 : _a.email });
    }
    // return {[unit._id]: {member}}
    async getByRid($in, params) {
        var _a;
        if (Acan.isObj($in))
            $in = Object.values($in);
        if (Acan.isEmpty($in))
            return {};
        const email = (_a = params.user) === null || _a === void 0 ? void 0 : _a.email;
        const rs = Acan.clone(await this.Model.find({ rid: { $in } }).select(['rid', 'members']));
        const list = {};
        rs.map((one) => {
            list[one.rid] = Object.assign({ collab: one._id, ...one.members.find((v) => v.email === email) });
        });
        return list;
    }
    async batchInvite(_id, data, params) {
        const { email, role, message } = data;
        if (Acan.isEmpty(email))
            return [];
        let fileId;
        const doc = await this.get(_id);
        if (!doc)
            return { message: 'not find data' };
        if (['task', 'pdTask', 'video'].includes(doc.type)) {
            fileId = await this.getFileId(doc, params);
            if (!fileId)
                return { message: (doc.type === 'video' ? 'video' : 'slides') + ' not created' };
        }
        const oldMembers = {};
        doc.members.map((v) => {
            oldMembers[v.email] = v._id;
        });
        const members = [];
        for (const v of email) {
            if (!v || oldMembers[v])
                continue; // has exist
            const member = { _id: new bson_1.ObjectID().toString(), email: v, nickname: v, role, status: true };
            const user = await this.app.service('users').byMail(v);
            if (user === null || user === void 0 ? void 0 : user.avatar)
                member.avatar = user.avatar;
            if (user === null || user === void 0 ? void 0 : user.nickname)
                member.nickname = user.nickname;
            members.push(member);
        }
        if (Acan.isEmpty(members))
            return [];
        await this.batchSend({ code: 'CollabInvite', members, message }, doc, params);
        if (fileId) {
            const post = { fileId, message, role };
            for (const one of members) {
                const rs = await this.shareWithEmail({ ...post, email: one.email }, params);
                logger_1.default.warn(rs, one.email);
                if (rs === null || rs === void 0 ? void 0 : rs.id)
                    one.permissionId = rs.id;
            }
        }
        await this.Model.updateOne({ _id }, { $addToSet: { members } });
        // logger.warn(_id, members, doc.type)
        return members;
    }
    async getFileId(doc, params) {
        if (!['task', 'pdTask', 'video'].includes(doc.type))
            return null;
        if (doc.type === 'video') {
            const { video } = await this.app.service('unit').Model.findById(doc.rid).select('video');
            return video;
        }
        else {
            const { sid } = await this.app.service('unit').Model.findById(doc.rid).select('sid');
            return sid;
        }
    }
    editUrl({ type, rid, pop }, params) {
        const editType = type.includes('video') ? 'video' : type.includes('unit') ? 'unit' : 'task';
        const ubj = new URL(`${SiteUrl}/v2/com/${editType}/edit/${rid}`);
        ubj.searchParams.set('back', '/home/<USER>');
        if (pop)
            ubj.searchParams.set('pop', pop);
        return ubj.href;
    }
    async collaberToAuthor(doc, { collaberName, message, code }, params) {
        var _a;
        const { nickname: author, email } = await this.app.service('users').uidToInfo(doc.uid);
        const { name: unitName } = await this.app.service('unit').Model.findById(doc.rid).select('name');
        const url = this.editUrl({ ...doc, pop: 'collab' }, params);
        this.app.service('notice-tpl').mailto(code, email, { url, unitName, author, collaberName, message }, (_a = params.user) === null || _a === void 0 ? void 0 : _a._id);
    }
    // author to collaber
    async batchSend(data, doc, params) {
        var _a;
        const { members, message, code } = data;
        const { rid, type } = doc;
        const { name: unitName } = await this.app.service('unit').Model.findById(rid).select('name');
        if (!unitName)
            return {};
        const { _id, nickname } = (_a = params.user) !== null && _a !== void 0 ? _a : {};
        for (const member of members) {
            const url = this.editUrl(doc, params);
            const post = { author: nickname, collaberName: member.nickname, url, unitName, message };
            if (code === 'CollabReviewReject')
                delete post.url;
            await this.app.service('notice-tpl').mailto(code, member.email, post, _id);
        }
        return {};
    }
    async getMemberInfo({ _id }, params) {
        return await this.Model.findOne({ 'members._id': _id }).select(['rid', 'type', 'uid', 'members.$']);
    }
    async getSharedMembers({}, params) {
        var _a;
        const list = await this.Model.distinct('members.email', { uid: (_a = params.user) === null || _a === void 0 ? void 0 : _a._id });
        const rs = [];
        for (const email of list) {
            rs.push(await this.app.service('users').byMail(email));
        }
        return rs;
    }
    async getApply({ _id, message }, params) {
        var _a;
        const { email, nickname, avatar } = (_a = params.user) !== null && _a !== void 0 ? _a : {};
        const doc = Acan.clone(await this.Model.findById(_id));
        const member = doc.members.find((m) => m.email === email);
        if (member)
            return { message: 'Already apply' };
        const members = { _id: new bson_1.ObjectID().toString(), email, avatar, nickname, status: false, message };
        const rs = await this.Model.updateOne({ _id }, { $addToSet: { members } });
        this.collaberToAuthor(doc, { code: 'CollabApply', collaberName: nickname, message }, params);
        return rs;
    }
    async getReview({ _id, role, status }, params) {
        const $set = { 'members.$.status': status };
        if (role)
            $set['members.$.role'] = role;
        const doc = await this.getMemberInfo({ _id }, params);
        if (status) {
            if (doc === null || doc === void 0 ? void 0 : doc.members[0]) {
                const { email } = doc.members[0];
                const fileId = await this.getFileId(doc, params);
                if (fileId) {
                    const rs = await this.shareWithEmail({ fileId, email, role, noticeEmail: true }, params);
                    if (rs === null || rs === void 0 ? void 0 : rs.id)
                        $set['members.$.permissionId'] = rs.id;
                }
            }
            this.batchSend({ code: 'CollabReviewAccept', members: doc.members }, doc, params);
        }
        return await this.Model.updateOne({ 'members._id': _id }, { $set });
    }
    // async shareWithAnyOne(doc: any, params: Params): Promise<any> {
    //   const fileId = await this.getFileId(doc, params)
    //   const drive = await this.app.service('slides').driveInit(params.user?.google)
    //   return await drive.permissions.create({
    //     fileId,
    //     requestBody: {
    //       role: 'reader',
    //       type: 'anyone',
    //     },
    //   })
    // }
    async autoJoin(doc, params) {
        var _a;
        const { email, nickname, avatar } = (_a = params.user) !== null && _a !== void 0 ? _a : {};
        const fileId = await this.getFileId(doc, params);
        const members = { _id: new bson_1.ObjectID().toString(), email, avatar, nickname, role: doc.guest, status: true };
        if (fileId) {
            const google = await this.app.service('users').getGoogle({ _id: doc.uid });
            const rs = await this.shareWithEmail({ fileId, email, role: doc.guest, noticeEmail: false, google }, params);
            if (rs === null || rs === void 0 ? void 0 : rs.id)
                members.permissionId = rs.id;
        }
        await this.Model.updateOne({ _id: doc._id }, { $addToSet: { members } });
        doc = Acan.clone(doc);
        doc.members.push(members);
        this.collaberToAuthor(doc, { code: 'CollabAutoJoin', collaberName: members.nickname }, params);
        logger_1.default.warn(members, 'autoJoin');
        return doc;
    }
    async shareWithEmail(data, params) {
        var _a;
        const { fileId, email, message, role, noticeEmail, google } = data;
        const drive = await this.app.service('slides').driveInit(google || ((_a = params.user) === null || _a === void 0 ? void 0 : _a.google));
        const post = {
            fileId,
            sendNotificationEmail: noticeEmail !== null && noticeEmail !== void 0 ? noticeEmail : true,
            supportsAllDrives: true,
            requestBody: {
                role: roleMap[role],
                type: 'user',
                emailAddress: email,
            },
        };
        if (message)
            post.emailMessage = message;
        logger_1.default.warn('shareWithEmail:', post);
        const rs = await drive.permissions.create(post).catch((e) => {
            logger_1.default.warn(e, e.message, post);
            return {};
        });
        logger_1.default.warn('shareWithEmail:', rs);
        return rs === null || rs === void 0 ? void 0 : rs.data;
    }
    async shareUpRole(doc, params) {
        var _a;
        if (!['task', 'pdTask', 'video'].includes(doc.type))
            return;
        const fileId = await this.getFileId(doc, params);
        if (!fileId)
            return logger_1.default.warn('no fileId');
        const member = Acan.clone(doc.members).find((v) => { var _a; return v._id === ((_a = params.query) === null || _a === void 0 ? void 0 : _a['members._id']); });
        if (!member || !member.permissionId)
            return logger_1.default.warn(member, params.query, 'shareUpRole error');
        const post = { fileId, permissionId: member.permissionId, requestBody: { role: roleMap[member.role] } };
        logger_1.default.warn(post, 'upRole');
        const drive = await this.app.service('slides').driveInit((_a = params.user) === null || _a === void 0 ? void 0 : _a.google);
        return await drive.permissions.update(post);
    }
    async shareRemove(memberId, params) {
        var _a;
        const doc = await this.Model.findOne({ 'members._id': memberId }).select(['id', 'rid', 'type', 'members.$']);
        if (!doc || !doc.members[0].permissionId) {
            this.batchSend({ code: 'CollabReviewReject', members: doc.members }, doc, params);
            return;
        }
        const fileId = await this.getFileId(doc, params);
        if (!fileId)
            return logger_1.default.warn('no fileId');
        const post = { fileId, permissionId: doc.members[0].permissionId };
        const drive = await this.app.service('slides').driveInit((_a = params.user) === null || _a === void 0 ? void 0 : _a.google);
        // 移除调用一次就行，失败也要正常返回
        const rs = await drive.permissions.delete(post).catch((e) => {
            logger_1.default.warn(e, e.message, post);
            return { message: e.message };
        });
        logger_1.default.warn(post, 'shareRemove', rs === null || rs === void 0 ? void 0 : rs.data, rs === null || rs === void 0 ? void 0 : rs.status);
        return rs;
    }
}
exports.Collab = Collab;
