// Initializes the `ambassador-auth` service on path `/ambassador-auth`
import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {AmbassadorA<PERSON>} from './ambassador-auth.class'
import createModel from '../../models/ambassador-auth.model'
import hooks from './ambassador-auth.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'ambassador-auth': AmbassadorAuth & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
  }

  // Initialize our service with any options it requires
  app.use('/ambassador-auth', new Ambassador<PERSON><PERSON>(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('ambassador-auth')

  service.hooks(hooks)
}
