"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'journal-comments';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, required: true },
        userType: { type: String, required: true },
        journalId: { type: String, required: true },
        content: { type: String, required: true },
        del: { type: Boolean, default: false },
    }, {
        timestamps: true,
    });
    schema.index({ journalId: 1 });
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
