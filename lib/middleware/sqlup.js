"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    // app.use('/sqlUp', async (req: any, res: any) => {
    //   const model = app.service('students').Model
    //   logger.warn(model, model.collection)
    //   res.json({rs: Object.keys(model), collection: Object.keys(model.collection)})
    // })
    app.use('/updateConf', async (req, res) => {
        const rs = await app.service('service-conf').Model.updateMany({ serviceRoles: null }, { $set: { serviceRoles: Agl.ServiceRoles } });
        res.json({ rs });
    });
    app.use('/updateAuth', async (req, res) => {
        const rs = await app.service('service-auth').Model.updateMany({ qualification: null }, { $set: { qualification: 'experiencedTeacher' } });
        res.json({ rs });
    });
    // app.use('/updateUsers', async (req: any, res: any) => {
    //   const rs = await app.service('users').Model.updateMany({freeServiceType: {$exists: true}}, {$unset: {freeServiceType: ''}})
    //   res.json({rs})
    // })
}
exports.default = default_1;
