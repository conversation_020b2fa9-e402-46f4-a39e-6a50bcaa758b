"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'manager';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String },
        email: { type: String, required: true, trim: true },
        name: { type: [String], trim: true },
        nickname: { type: String, trim: true },
        avatar: { type: String, trim: true },
        role: { type: String, trim: true, enum: Agl.usersManagerRoles },
        status: { type: Number, default: 0 },
        del: { type: Boolean, default: false },
        joinAt: { type: Date },
    }, {
        timestamps: true,
    });
    schema.index({ email: 1 }, { unique: true });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
