"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'classApply';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        student: { type: String, trim: true, required: true },
        type: { type: String, default: 'subject', enum: Agl.classesTypes },
        school: { type: String, required: true, trim: true },
        class: { type: String, trim: true, required: true },
        answers: { type: Schema.Types.Mixed },
        status: { type: Number, default: 0 }, // 0: pending, 1: approved, -1: rejected
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
