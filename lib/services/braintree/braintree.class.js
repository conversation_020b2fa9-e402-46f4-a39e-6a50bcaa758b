"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Braintree = void 0;
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
const braintreeSettings = require('../../../config/braintree.json');
const braintreeSetting = !isDev ? braintreeSettings.live : braintreeSettings.sandbox;
const braintree = require('braintree');
const gateway = new braintree.BraintreeGateway({
    environment: braintree.Environment.Sandbox,
    merchantId: braintreeSetting.MerchantID,
    publicKey: braintreeSetting.PublicKey,
    privateKey: braintreeSetting.PrivateKey,
});
class Braintree {
    constructor(options = {}, app) {
        this.options = options;
        this.app = app;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async find(params) {
        return [];
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async get(id, params) {
        return {
            id,
            text: `A new message with ID: ${id}!`,
        };
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async create(data, params) {
        if (Array.isArray(data)) {
            return Promise.all(data.map((current) => this.create(current, params)));
        }
        return data;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async update(id, data, params) {
        return data;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async patch(id, data, params) {
        return data;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async remove(id, params) {
        return { id };
    }
    async getToken({ id }, params) {
        let res = await gateway.clientToken.generate({});
        return res;
    }
    async getSale({ nonce, deviceData, orderId }, params) {
        if (!nonce || !deviceData || !orderId) {
            return Promise.reject(new GeneralError({ message: 'Params error' }));
        }
        const order = await this.app.service('order').get(orderId);
        if (order.status !== 100) {
            return Promise.reject(new GeneralError({ message: 'Order status error' }));
        }
        let res;
        try {
            res = await gateway.transaction.sale({
                amount: (Number(order.price) / 100).toFixed(2),
                paymentMethodNonce: nonce,
                deviceData: deviceData,
                // customerId: order.buyer,
                options: {
                    submitForSettlement: true,
                },
                orderId: orderId,
            });
        }
        catch (e) {
            res = e;
        }
        if (res.success) {
            let paymentInfo = {
                paymentInstrumentType: res.transaction.paymentInstrumentType,
                cardType: res.transaction.creditCard.cardType || '',
                last4: res.transaction.creditCard.last4 || '',
            };
            await this.app.service('order').patch(orderId, { braintreeId: res.transaction.id, $push: { payMethod: 'braintree' }, paymentInfo, paidAt: new Date() });
            await this.app.service('order').completeOrder(order);
            return res;
        }
        else {
            return Promise.reject(new GeneralError({ message: res.message }));
        }
    }
    async getRefund({ id, amount }, params) {
        let res;
        try {
            res = await gateway.transaction.refund(id, amount);
        }
        catch (e) {
            res = e;
        }
        if (res.success) {
            return {
                success: true,
                message: 'refund succeeded',
            };
        }
        else {
            return {
                success: false,
                message: res.message,
            };
        }
    }
    async getDetail({ id }, params) {
        let res;
        try {
            res = await gateway.transaction.find(id);
        }
        catch (e) {
            res = e;
        }
        return res;
    }
    // async getUpdateOrder({id}: any, params: Params): Promise<Data> {
    //   let res
    //   let dd = await gateway.transaction.find('c6msvmsn')
    //   try {
    //     // res = await gateway.transaction.find('etxg26dm')
    //     res = await gateway.transaction.updateDetails('c6msvmsn')
    //   } catch (e) {
    //     res = e
    //   }
    //   return res
    // }
    async getCustomer({ id }, params) {
        let res;
        try {
            res = await gateway.customer.find('658c1e451857ef1e012846c3');
        }
        catch (e) {
            res = e;
        }
        return res;
    }
}
exports.Braintree = Braintree;
