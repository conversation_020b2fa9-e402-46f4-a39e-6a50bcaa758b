import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'

export class Ambassador<PERSON><PERSON> extends Service {
  app: Application

  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async ext(o: any) {
    if (o.uid) o.owner = await this.app.service('users').uidToInfo(o.uid)
  }

  async send(doc: any, tpl: string, params: Params): Promise<any> {
    const {uid, reason} = doc
    let user = await this.app.service('users').uidToInfo(uid)
    let url = ''
    if (tpl === 'SchoolAmbassadorApproved') {
      // url = `${SiteUrl}/v2/service/pack/${servicePack}`
    } else if (tpl === 'SchoolAmbassadorRejected') {
      // url = `${SiteUrl}/v2/service/pack/${servicePack}`
    }
    return await this.app.service('notice-tpl').mailto(tpl, user, {username: user.name.join(' '), url, reason}, params.user?._id)
  }
}
