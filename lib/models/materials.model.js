"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'materials';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, index: true },
        id: { type: String, index: true, required: true, trim: true },
        page: { type: String, required: true, trim: true },
        list: [
            {
                question: { type: String, trim: true },
                type: { type: String, trim: true },
                url: { type: String, trim: true },
                key: { type: String, trim: true },
                size: { type: Number, trim: true },
                desc: { type: String, trim: true },
                ext: { type: Schema.Types.Mixed },
                position: { type: Schema.Types.Mixed },
                slideMatrerial: { type: Boolean },
                dimensions: { type: Schema.Types.Mixed },
                slideWidth: { type: Number },
                slideHeight: { type: Number }, // slide height
            },
        ],
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
