"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'toolData';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        unit: { type: String, trim: true },
        task: { type: String, trim: true },
        tool: { type: String, trim: true },
        session: { type: String, trim: true },
        assessor: { type: String, required: true },
        student: { type: String, required: true },
        data: { type: Schema.Types.Mixed },
        filled: { type: Boolean, default: false }, // All forms have been filled
    }, {
        timestamps: true
    });
    schema.index({ tool: 1, session: 1, assessor: 1, student: 1 }, { unique: true });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
