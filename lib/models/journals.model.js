"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'journals';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        sessionId: { type: String, required: true },
        isWorkshop: { type: Boolean, default: false },
        classId: { type: String, required: true },
        studentId: { type: String, required: true },
        teacherId: { type: String, required: true },
        studentInfo: {
            name: { type: [String], trim: true },
            avatar: { type: String, trim: true },
        },
        teacherInfo: {
            name: { type: [String], trim: true },
            avatar: { type: String, trim: true },
        },
        teachers: { type: [String], default: [] },
        subjects: { type: [Schema.Types.Mixed], default: [] },
        tagIds: { type: [String], default: [] },
        question: { type: Schema.Types.Mixed },
        response: { type: Schema.Types.Mixed },
        status: { type: Number, default: 0 },
        teacherReason: { type: String, default: '' },
        studentReason: { type: String, default: '' },
        feedbacks: { type: [Schema.Types.Mixed], default: [] },
        visibility: {
            type: String,
            enum: ['students', 'parents', 'students-parents', 'private'],
            default: 'students-parents',
        },
        comments: {
            count: { type: Number, default: 0 },
            latest_comments: { type: [Schema.Types.Mixed], default: [] },
        },
        likes: { type: Number, default: 0 },
        del: { type: Boolean, default: false },
        publishedAt: { type: Date },
    }, {
        timestamps: true,
    });
    schema.index({ classId: 1, studentId: 1 }, { unique: false });
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
