"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'agreement';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        name: { type: String },
        type: { type: String, required: true, enum: ['all_users', 'service_provider', 'agents', 'education_consultants', 'schools'] },
        mode: {
            type: String,
            required: true,
            enum: [
                'privacy',
                'terms_and_conditions',
                'cancellation',
                'points_redemption',
                'verification',
                'service_premium',
                'agents',
                'consultation',
                'pipeline',
                'content_provider',
                'on_campus',
                'associatedTaskCancellationAndRefund',
            ],
        },
        content: { type: String },
        status: { type: Number, default: 1 }, // 0: archived, 1: current
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
