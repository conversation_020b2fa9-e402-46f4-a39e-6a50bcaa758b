import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {ObjectID} from 'bson'
import logger from '../../logger'

export class UnitTpl extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
  getName({_id}: {_id: string}, params?: Params) {
    return this.Model.findById(_id).select('name')
  }
  getPdTask({}, params: Params) {
    return this.Model.findOne({mode: 'pdTask', uid: '1'})
  }
  getPdUnit({}, params: Params) {
    return this.Model.findOne({mode: 'pdUnit', uid: '1'})
  }
  getTask({curriculum}: {curriculum: String}, params: Params) {
    return this.Model.findOne({mode: curriculum === 'pd' ? 'pdTask' : 'task', curriculum, school: '1'})
  }
  getUnit({curriculum}: {curriculum: String}, params: Params) {
    return this.Model.findOne({mode: curriculum === 'pd' ? 'pdUnit' : 'unit', curriculum, school: '1'})
  }
  getCopyTpl(query: any, params: Params) {
    return this.copyTpl(query, params)
  }
  async copyTpl({curricId, school}: any, params: Params) {
    const group = new ObjectID().toString()
    const {code}: any = (await this.app.service('curric').Model.findById(curricId).select('code')) || {}
    const curriculum = code || 'others'
    let doc = Acan.clone(await this.getUnit({curriculum}, params))
    if (!doc) return Promise.reject(new Error('curriculum code not found unit: ' + curriculum))
    const rs: any = {}
    if (code === 'pd') {
      rs.unit = {_id: doc._id}
    } else {
      doc.school = school || params.user?._id
      doc.group = group
      doc.curricId = curricId
      for (const k of ['createdAt', 'updatedAt', '_id']) {
        delete doc[k]
      }
      rs.unit = await this.create(doc)
    }
    doc = Acan.clone(await this.getTask({curriculum}, params))
    if (!doc) return Promise.reject(new Error('curriculum code not found task: ' + curriculum))
    if (code === 'pd') {
      rs.task = {_id: doc._id}
    } else {
      doc.school = school || params.user?._id
      doc.group = group
      doc.curricId = curricId
      for (const k of ['createdAt', 'updatedAt', '_id']) {
        delete doc[k]
      }
      rs.task = await this.create(doc)
    }
    return rs
  }
  async mergePubTpl(doc: any) {
    const rs = this.app.get(`${doc.mode.toFirstUpperCase()}Tpl`)
    if (!rs) return logger.error('Not found pub tpl in mode:' + doc.mode)
    logger.info(`Found mode: ${doc.mode} pub tpl`, rs.length)
    const list: any = {}
    for (const o of rs) {
      list[o.code] = o
    }
    for (const o of doc.data) {
      if (list[o.code]) Object.assign(o, Object.assign(list[o.code], o))
    }
  }
  async snapshot(_id: String) {
    logger.info('unit-tpl.snapshot', _id)
    if (!_id) return {}
    const doc: any = Acan.clone(await this.Model.findById(_id))
    if (!doc) return {}
    const tags: any = {}
    const types = ['checkbox', 'choice', 'choice-mark']
    let $in = doc.data.filter((v: any) => v.enable && !v.code && !v.tags && types.includes(v.type)).map((v: any) => v.name)
    if (!Acan.isEmpty($in)) {
      const arr = Acan.clone(await this.app.service('tags').Model.find({uid: '1', set: $in}).select('snapshot'))
      for (const o of arr) {
        if (!o.snapshot) continue
        tags[o.set] = o.snapshot.child
      }
    }
    $in = doc.data.filter((v: any) => v.enable && v.tags).map((v: any) => v.tags)
    if (!Acan.isEmpty($in)) {
      const arr = Acan.clone(await this.app.service('tags').Model.find({_id: $in}).select('snapshot'))
      for (const o of arr) {
        if (!o.snapshot) continue
        tags[o._id] = o.snapshot.child
      }
    }
    const info = Acan.clone(doc)
    delete info.data
    await this.mergePubTpl(doc)
    return {
      tags,
      template: doc.data,
      templateInfo: info,
    }
  }
  async getSnapshot({_id}: any, params: Params) {
    return this.snapshot(_id)
  }
  async patchSetDefault({school, group}: any, params: Params) {
    const uid = params.user?._id.toString()
    const key = uid === school ? 'personal' : school
    const confDocs = Acan.clone(await this.app.service('conf-user').Model.find({uid, key: {$in: ['UnitTplDefault', 'TaskTplDefault']}}))
    const conf: any = {}
    const keyMap: any = {TaskTplDefault: 'task', UnitTplDefault: 'unit'}
    confDocs.map((v: any) => {
      conf[keyMap[v.key]] = v._id
    })
    const arr: any = Acan.clone(await this.Model.find({group}).select('mode curricId curriculum'))
    const rs = []
    for (const o of arr) {
      const $set = {[`val.${key}`]: o._id}
      rs.push(await this.app.service('conf-user').Model.updateOne({_id: conf[o.mode]}, {$set}))
    }
    return {ok: 1, rs}
  }
}
