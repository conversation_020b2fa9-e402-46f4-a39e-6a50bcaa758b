"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'journal-likes';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, required: true },
        journalId: { type: String, required: true },
        del: { type: Boolean, default: false }, // Soft delete
    }, {
        timestamps: true,
    });
    // Ensure one user can only like a journal once
    schema.index({ journalId: 1, uid: 1 }, { unique: true });
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
