"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClassQuestionLogs = void 0;
const feathers_mongoose_1 = require("feathers-mongoose");
class ClassQuestionLogs extends feathers_mongoose_1.Service {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    constructor(options, app) {
        super(options);
        this.app = app;
    }
    async updateLogs({ questions }, params) {
        var _a, _b;
        const user = (_a = params.user) !== null && _a !== void 0 ? _a : {};
        let log = await this.Model.findOne({ _id: user._id });
        let oldQuestions = (_b = log === null || log === void 0 ? void 0 : log.questions) !== null && _b !== void 0 ? _b : [];
        oldQuestions = oldQuestions.filter((e) => !questions.includes(e));
        oldQuestions.unshift(...questions);
        let insertQuestions = oldQuestions.slice(0, 10);
        await this.Model.findOneAndUpdate({ _id: user._id }, { questions: insertQuestions }, { upsert: true });
    }
}
exports.ClassQuestionLogs = ClassQuestionLogs;
