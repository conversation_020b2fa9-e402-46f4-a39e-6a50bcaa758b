import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import logger from '../../logger'

export class Reflection extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
  async getFixedTo() {
    const count = await this.Model.count({to: {$ne: null}, 'to.0': {$exists: false}}).sort({_id: -1})
    const rs = await this.Model.updateMany({to: {$ne: null}, 'to.0': {$exists: false}}, {$unset: {to: ''}})
    return {rs, count}
  }
  async extUser(one: any, params?: Params) {
    if (!one.uid) return
    one.user = await this.app.service('users').uidToInfo(one.uid)
    if (one.school) {
      const rs = await this.app.service('school-user').getInfo({email: one.user.email, school: one.school})
      one.user = rs || one.user
    }
  }
  async extFiles(one: any, params?: Params) {
    if (Acan.isEmpty(one.attach)) return
    one.files = await this.app.service('files').userFileInfo({arr: one.attach, uid: one.uid}, params)
  }
  // 发布到library的数据
  async unitSnapshot(unit: String, params: Params) {
    const rs = Acan.clone(await this.app.service('reflection').Model.find({unit, public: true, to: []}))
    await this.extData(rs, params)
    return rs
  }
  async extData(data: any, params?: Params) {
    if (Array.isArray(data)) {
      for (const one of data) {
        await this.extUser(one, params)
        await this.extFiles(one, params)
      }
    } else {
      await this.extUser(data, params)
      await this.extFiles(data, params)
    }
    return data
  }
}
