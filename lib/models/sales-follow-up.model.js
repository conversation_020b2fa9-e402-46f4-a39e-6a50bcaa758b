"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'salesFollowUp';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        customer: { type: String, required: true },
        customerName: { type: String },
        customerType: { type: String, default: 'user', enum: ['user', 'school'] },
        type: { type: String, default: 'following', enum: ['following', 'completed'] },
        status: { type: Number, default: 0 },
        servicePack: { type: String, required: true },
        servicePackName: { type: String },
        serviceType: { type: String, enum: ['mentoring', 'correcting', 'substitute', 'service_premium'] },
        servicePackUser: { type: String, required: true },
        order: { type: String },
        sales: { type: String },
        salesName: { type: String },
        salesType: { type: String, enum: ['manager', 'consultant'] },
        // 联系记录
        contactLog: [
            {
                duration: { type: Number },
                createdAt: { type: Date, default: Date.now },
                sales: { type: String },
                salesName: { type: String },
                note: { type: String },
            },
        ],
        followedAt: { type: Date },
        releasedAt: { type: Date },
        shareCount: { type: Number, default: 0 },
        shareGoods: { type: [String] }, // 已分享的推荐商品
    }, {
        timestamps: true,
    });
    schema.index({ customer: 1, servicePackUser: 1 }, { unique: true });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
