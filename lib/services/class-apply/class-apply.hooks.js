"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const { NotFound, GeneralError, BadRequest } = require('@feathersjs/errors');
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [],
        get: [hook_1.default.toClass],
        create: [
            async (d) => {
                var _a;
                let { student, class: classId, answers } = d.data;
                let classData = await d.app.service('classes').Model.findOne({ _id: classId });
                // 只能有一个未拒绝的申请
                let applyData = await d.service.Model.findOne({ student: student, class: classId, status: { $ne: -1 } });
                if (applyData) {
                    return Promise.reject(new GeneralError({ message: 'Student has applied for this class' }));
                }
                // 自主报名开启判断
                if (!classData.enroll.enable) {
                    return Promise.reject(new GeneralError({ message: 'Class self-enroll not open' }));
                }
                if (!classData.approvalEnable) {
                    // 审核未开 直接通过
                    d.data.status = 1;
                }
                else {
                    // 审核开启 验证答案字段
                    if (!d.data.answers) {
                        return Promise.reject(new GeneralError({ message: 'Class apply answers is required' }));
                    }
                    for (let i = 0; i < classData.questions.length; i++) {
                        const q = classData.questions[i];
                        if (!answers.some((a) => a.question == q)) {
                            return Promise.reject(new GeneralError({ message: 'Class apply answers data error' }));
                        }
                    }
                }
                // 学科班人数/截止日期判断
                if (d.data.status == 1) {
                    let classData = await d.app.service('classes').Model.findOne({ _id: classId });
                    let count = ((_a = classData === null || classData === void 0 ? void 0 : classData.count) === null || _a === void 0 ? void 0 : _a.student) || 0;
                    if (classData.maxParticipants && classData.maxParticipants <= count) {
                        return Promise.reject(new GeneralError({ message: 'Class apply full' }));
                    }
                    if (classData.deadline && new Date(classData.deadline) < new Date()) {
                        return Promise.reject(new GeneralError({ message: 'Class apply end' }));
                    }
                }
            },
        ],
        update: [],
        patch: [
            async (d) => {
                var _a;
                let origin = await d.service.Model.findOne({ _id: d.id });
                let { student, class: classId } = origin;
                // 学科班人数/截止日期判断
                if (d.data.status == 1) {
                    let classData = await d.app.service('classes').Model.findOne({ _id: classId });
                    let count = ((_a = classData === null || classData === void 0 ? void 0 : classData.count) === null || _a === void 0 ? void 0 : _a.student) || 0;
                    if (classData.maxParticipants && classData.maxParticipants <= count) {
                        return Promise.reject(new GeneralError({ message: 'Class apply full' }));
                    }
                    if (classData.deadline && new Date(classData.deadline) < new Date()) {
                        return Promise.reject(new GeneralError({ message: 'Class apply end' }));
                    }
                }
                if (d.data.status == -1) {
                    if (origin.status == 1) {
                        // approve -> reject
                        await d.app.service('students').patch(student, { $pull: { subjectClass: classId } });
                        d.service.send(origin, 'StudentRemovedFromSubjectClass', d.params);
                    }
                    else if (origin.status == 0) {
                        // pending -> reject
                        d.service.send(origin, 'StudentRejectedToBeAddedToASubjectClass', d.params);
                    }
                }
            },
        ],
        remove: [],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                if (d.result) {
                    for (let i = 0; i < d.result.data.length; i++) {
                        await d.service.extData(d.result.data[i]);
                    }
                }
            },
        ],
        get: [
            async (d) => {
                if (hook_1.default.classExist(d))
                    return d;
                await d.service.extData(d.result);
            },
        ],
        create: [
            async (d) => {
                let { status } = d.data;
                let { student, class: classId } = d.result;
                if (status == 1) {
                    await d.app.service('students').patch(student, { $addToSet: { subjectClass: classId } });
                    d.service.send(d.result, 'StudentAddedToSubjectClass', d.params);
                }
            },
        ],
        update: [],
        patch: [
            async (d) => {
                let { status } = d.data;
                let { student, class: classId } = d.result;
                if (status == 1) {
                    await d.app.service('students').patch(student, { $addToSet: { subjectClass: classId } });
                    d.service.send(d.result, 'StudentAddedToSubjectClass', d.params);
                }
            },
        ],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
