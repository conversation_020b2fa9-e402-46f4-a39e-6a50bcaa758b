"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'suspendLogs';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String, required: true },
        nickname: { type: String, sparse: true, trim: true },
        type: { type: String, enum: ['teaching-accident', 'frequent-cancellation', '0-satisfaction-rate'] },
        accident: { type: String },
        days: { type: Number },
        withdraw: { type: Boolean, default: false }, // 撤销
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
