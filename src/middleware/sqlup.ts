import {Application} from '../declarations'
import logger from '../logger'

export default function (app: Application): void {
  app.use('/unit.service.type', async (req: any, res: any) => {
    const {$in} = req.query ?? {}
    const codeMaps = app.get('pdSubjectCodeMap')
    const con = {curriculum: 'pd', 'service.type': {[$in ? '$in' : '$nin']: Object.keys(codeMaps)}}
    const total = await app.service('unit').Model.count(con)
    const data: any = await app.service('unit').Model.find(con).limit(50).sort({_id: -1}).select('service createdAt')

    const id2Code = app.get('pdSubjectId2Code')
    const ups = []
    for (const o of data) {
      let arr = o.service.type.map((v: any) => id2Code[v])
      arr = Acan.objClean(arr)
      const $set = {'service.type': arr}
      ups.push($set)
      // await Model.updateOne({_id: o._id}, {$set})
    }
    res.json({total, ups, data})
  })
  app.use('/auth.service.type', async (req: any, res: any) => {
    const Model = app.service('service-auth').Model
    const {$in} = req.query ?? {}
    const codeMaps = app.get('pdSubjectCodeMap')
    const con = {curriculum: 'pd', 'unitSnapshot._id': {$exists: true}, 'unitSnapshot.service.type': {[$in ? '$in' : '$nin']: Object.keys(codeMaps)}}
    const total = await Model.count(con)
    const data: any = await Model.find(con)
      .limit(10)
      .sort({_id: -1})
      .select(['mentoringType', 'subject', 'unitSnapshot.service.type', 'linkSnapshot', 'createdAt'])
    for (const o of data) {
      for (const id in o.linkSnapshot) {
        if (Acan.isEmpty(o.linkSnapshot[id].service?.type)) {
          delete o.linkSnapshot[id]
          continue
        }
        o.linkSnapshot[id] = {service: o.linkSnapshot[id].service}
      }
    }
    const id2Code = app.get('pdSubjectId2Code')
    const ups = []
    for (const o of data) {
      let arr = o.unitSnapshot.service.type.map((v: any) => id2Code[v])
      arr = Acan.objClean(arr)
      const $set: any = {'unitSnapshot.service.type': arr}
      for (const id in o.linkSnapshot) {
        const {service} = o.linkSnapshot[id]
        if (!Acan.isEmpty(service?.type)) {
          $set[['linkSnapshot', id, 'service.type'].join('.')] = service.type.map((v: any) => id2Code[v])
        }
      }
      ups.push($set)
      // await Model.updateOne({_id: o._id}, {$set})
    }
    res.json({total, ups, data})
  })
  app.use('/session.service.type', async (req: any, res: any) => {
    const Model = app.service('session').Model
    const {$in} = req.query ?? {}
    const codeMaps = app.get('pdSubjectCodeMap')
    const con = {
      'task.curriculum': 'pd',
      'task.service.type.0': {$exists: true},
      'task.service.type': {[$in ? '$in' : '$nin']: Object.keys(codeMaps)},
    }
    const total = await Model.count(con)
    const data: any = await Model.find(con).limit(50).sort({_id: -1}).select(['type', 'cid', 'subject', 'task._id', 'task.service', 'createdAt'])

    const id2Code = app.get('pdSubjectId2Code')
    const ups = []
    for (const o of data) {
      let arr = o.task.service.type.map((v: any) => id2Code[v])
      arr = Acan.objClean(arr)
      const $set = {'task.service.type': arr}
      ups.push($set)
      // await Model.updateOne({_id: o._id}, {$set})
    }
    res.json({total, ups, data})
  })
  // app.use('/sqlUp', async (req: any, res: any) => {
  //   const model = app.service('students').Model
  //   logger.warn(model, model.collection)
  //   res.json({rs: Object.keys(model), collection: Object.keys(model.collection)})
  // })
  app.use('/updateConf', async (req: any, res: any) => {
    const rs = await app.service('service-conf').Model.updateMany({serviceRoles: null}, {$set: {serviceRoles: Agl.ServiceRoles}})
    res.json({rs})
  })
  app.use('/updateAuth', async (req: any, res: any) => {
    const rs = await app.service('service-auth').Model.updateMany({qualification: null}, {$set: {qualification: 'experiencedTeacher'}})
    res.json({rs})
  })
  // app.use('/updateUsers', async (req: any, res: any) => {
  //   const rs = await app.service('users').Model.updateMany({freeServiceType: {$exists: true}}, {$unset: {freeServiceType: ''}})
  //   res.json({rs})
  // })
}
