"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'servicePack';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        name: { type: String, trim: true },
        cover: { type: String, trim: true },
        coverName: { type: String, trim: true },
        points: { type: [String], trim: true },
        type: { type: String, required: true, enum: Agl.ServiceType },
        serviceRoles: { type: String, enum: Agl.ServiceRoles },
        mentoringType: { type: String, enum: Agl.MentoringType },
        countryCode: { type: [String], trim: true },
        curriculum: { type: String, trim: true },
        subject: { type: [String], trim: true },
        topic: { type: [String], trim: true },
        gradeGroup: { type: [String], trim: true },
        qualification: { type: String, trim: true },
        // 主题服务包 支持关联多个课件 https://github.com/zran-nz/bug/issues/4861
        contentOrientatedEnable: { type: Boolean, default: false },
        contentOrientatedConfig: {
            price: { type: Number },
            schoolPrice: { type: Number, trim: true }, // 给机构的价格 单次价格 *100，cc，美分
        },
        contentOrientated: [
            {
                premium: { type: String, trim: true },
                subject: { type: String, trim: true },
                times: { type: Number },
                price: { type: Number, trim: true },
                schoolPrice: { type: Number, trim: true },
                servicePack: { type: String },
                message: { type: String }, // 认证课不正常的时候提示
            },
        ],
        consultant: {
            // 顾问配置
            type: { type: String, enum: Agl.ConsultantType },
            carerService: { type: String },
            servicePack: { type: String }, // 捆绑的服务包, service-pack._id 服务包id
        },
        interviewPack: {
            _id: { type: String },
            times: { type: Number }, // 次数
        },
        carerPack: {
            _id: { type: String },
            times: { type: Number }, // 次数
        },
        price: { type: Number, trim: true },
        discount: [
            {
                count: { type: Number },
                discount: { type: Number },
                gifts: { type: Number, default: 0 }, // 免费赠送的次数
            },
        ],
        discountConfig: {
            // 折扣配置
            enable: { type: Boolean, default: false },
            end: { type: Date },
            discount: { type: Number }, // 主题服务包 统一折扣 %
        },
        freq: { type: Number, enum: [7, 14, 30, 120] },
        duration: { type: Number },
        break: { type: Number },
        status: { type: Boolean, default: false },
        lastPublished: { type: Date },
        count: {
            // 统计
            sold: { type: Number },
            valid: { type: Number },
            ticket: { type: Number }, // 有效代金券数量
        },
        attachments: [
            {
                // 图片/视频，附件，推广素材, 插入到课堂的时候，显示在素材中
                filename: { type: String, trim: true },
                mime: { type: String, trim: true },
                hash: { type: String, trim: true },
                videoType: { type: String, enum: Agl.ServicePackVideoType }, // 视频的类型
            },
        ],
        income: { type: Number, default: 0 },
        statistic: [
            {
                count: { type: Number },
                orderCount: { type: Number, default: 0 },
                income: { type: Number, default: 0 },
                isSchool: { type: Boolean, default: false },
                type: { type: String, enum: ['lecture', 'mentor', 'all'] },
                city: { type: String }, // 线下 城市
            },
        ],
        salesTarget: { type: [String], enum: Agl.ServiceSalesTarget },
        backgroundCheck: { type: Boolean, default: false },
        requirements: { type: String, default: false },
        requirementsItems: { type: [String] },
        interview: { type: Boolean, default: false },
        splitSale: { type: Boolean, default: false },
        filled: { type: Boolean, default: false },
        reason: { type: String },
        keywords: { type: [String] },
        isOnCampus: { type: Boolean, default: false },
        country: { type: String, trim: true },
        onCampusPrice: [
            {
                hash: { type: String, trim: true },
                city: { type: String, trim: true },
                price: { type: Number, trim: true },
                discount: [
                    {
                        count: { type: Number },
                        discount: { type: Number },
                        gifts: { type: Number, default: 0 }, // 免费赠送的次数
                    },
                ],
            },
        ],
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
