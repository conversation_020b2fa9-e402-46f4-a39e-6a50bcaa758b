import {Request, Response} from 'express'
export default function (app: any): void {
  app.post('/paypal/completed', async (req: Request, res: Response) => {
    const {event_type, resource} = req.body
    if (event_type === 'PAYMENT.CAPTURE.COMPLETED') {
      await app.service('paypal-webhook').Model.create({body: req.body})
      const orderId = resource.invoice_id
      if (!orderId) {
        return res.json({
          code: 200,
        })
      }
      await app.service('order').processOrderCompletion(orderId, {
        paypalId: resource.id,
        settled: true,
        $push: {payMethod: 'paypal'},
        paymentInfo: {
          paymentInstrumentType: 'paypal_account',
          cardType: '',
          last4: '',
        },
        paidAt: new Date(),
      })
    }
    res.json({
      code: 200,
    })
  })
}
