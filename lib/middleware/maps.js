"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = __importDefault(require("../logger"));
const got = require('got');
const apiKey = 'AIzaSyDh-aLBR7Gzd8-XQOKQAdoExwlIoi_AHEk';
function default_1(app) {
    const googleConf = app.get('google');
    app.all('/maps/place/:country', async (req, res) => {
        var _a;
        const { country } = req.params;
        const { q } = (_a = req.query) !== null && _a !== void 0 ? _a : {};
        const rs = (await got(`https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${q}&components=country:${country}&key=${apiKey}`, { json: true }))
            .body;
        res.json(rs);
    });
    app.all('/maps/city/:country', async (req, res) => {
        const { country } = req.params;
        const { q } = req.query;
        const rs = (await got(`https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${q}&components=country:${country}&key=${apiKey}&types=locality`, {
            json: true,
        })).body;
        logger_1.default.warn(rs, 123123);
        const arr = [];
        rs.predictions.map((v) => {
            arr.push({ desc: v.description, terms: v.terms.map((t) => t.value) });
        });
        res.json(arr);
    });
    app.all('/maps/address/:country', async (req, res) => {
        const { country } = req.params;
        const { q } = req.query;
        const rs = (await got(`https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${q}&components=country:${country}&key=${apiKey}&types=address`, {
            json: true,
        })).body;
        const arr = [];
        rs.predictions.map((v) => {
            arr.push({ desc: v.description, terms: v.terms.map((t) => t.value), place_id: v.place_id });
        });
        res.json(arr);
    });
    app.all('/google/image', async (req, res) => {
        var _a;
        let { q, type, color, start = 0, size = 'large' } = (_a = req.query) !== null && _a !== void 0 ? _a : {};
        q = q.trim().replace(/\s/g, '+');
        if (!q)
            return res.json([]);
        const baseUrl = 'https://customsearch.googleapis.com/customsearch/v1';
        const headers = { 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; rv:78.0) Gecko/20100101 Firefox/78.0' };
        const url = new URL(baseUrl);
        url.searchParams.set('q', q);
        url.searchParams.set('searchType', 'image');
        url.searchParams.set('cx', googleConf.cseid);
        url.searchParams.set('key', googleConf.key);
        // url.searchParams.set('fileType', 'png,gif,jpg,jpeg')
        if (start)
            url.searchParams.set('start', start);
        if (size)
            url.searchParams.set('imgSize', size);
        if (type)
            url.searchParams.set('imgType', type);
        if (color)
            url.searchParams.set('imgDominantColor', color);
        // url.searchParams.set('safe', safe)
        // url.searchParams.set('imgColorType', colorType)
        const rs = (await got(url.href, { headers, json: true })).body;
        res.json(rs.items.map(({ mime, link, image, snippet }) => {
            return {
                mime,
                width: image.width,
                height: image.height,
                size: image.byteSize,
                url: link,
                thumbnail: {
                    url: image.thumbnailLink,
                    width: image.thumbnailWidth,
                    height: image.thumbnailHeight,
                },
                description: snippet,
                parentPage: image.contextLink,
            };
        }));
    });
    app.all('/google/suggest', async (req, res) => {
        var _a;
        // `client` is a required parameter. `client=firefox` returns the smallest result.
        const baseUrl = 'https://suggestqueries.google.com/complete/search?client=firefox&ds=yt';
        const headers = { 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; rv:78.0) Gecko/20100101 Firefox/78.0' };
        let { q, locale } = (_a = req.query) !== null && _a !== void 0 ? _a : {};
        q = q.trim();
        if (!q)
            return res.json([]);
        locale = locale || 'en';
        const url = new URL(baseUrl);
        url.searchParams.set('q', q);
        url.searchParams.set('hl', locale);
        logger_1.default.log(url.href);
        const rs = (await got(url.href, { headers, json: true })).body;
        res.json(rs);
    });
    app.all('/google/youtube', async (req, res) => {
        var allowedProperties = [
            'fields',
            'channelId',
            'channelType',
            'eventType',
            'forContentOwner',
            'forDeveloper',
            'forMine',
            'location',
            'locationRadius',
            'onBehalfOfContentOwner',
            'order',
            'pageToken',
            'publishedAfter',
            'publishedBefore',
            'regionCode',
            'relatedToVideoId',
            'relevanceLanguage',
            'safeSearch',
            'topicId',
            'type',
            'videoCaption',
            'videoCategoryId',
            'videoDefinition',
            'videoDimension',
            'videoDuration',
            'videoEmbeddable',
            'videoLicense',
            'videoSyndicated',
            'videoType',
            'key',
            'q',
        ];
        const baseUrl = 'https://www.googleapis.com/youtube/v3/search';
        const headers = { 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; rv:78.0) Gecko/20100101 Firefox/78.0' };
        const url = new URL(baseUrl);
        url.searchParams.set('key', googleConf.key);
        // url.searchParams.set('order', 'relevance') // date, rating,relevance,title,videoCount,viewCount
        for (const key in req.query) {
            if (!allowedProperties.includes(key))
                continue;
            url.searchParams.set(key, req.query[key] + '');
        }
        if (!req.query.part)
            url.searchParams.set('part', 'snippet');
        if (!req.query.maxResults)
            url.searchParams.set('maxResults', '30');
        logger_1.default.warn('google youtube search:', url.href);
        const rs = (await got(url.href, { headers, json: true })).body;
        res.json(rs);
    });
    app.all('/urlDown', async (req, res) => {
        var _a;
        const { url } = (_a = req.query) !== null && _a !== void 0 ? _a : {};
        const rs = {};
        res.json(rs);
    });
}
exports.default = default_1;
