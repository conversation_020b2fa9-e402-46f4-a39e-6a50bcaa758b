"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication_1 = require("@feathersjs/authentication");
const { authenticate } = authentication_1.hooks;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bson_1 = require("bson");
const logger_1 = __importDefault(require("../../logger"));
const modGet_1 = __importDefault(require("./modGet"));
const hook_1 = __importDefault(require("../../hook"));
const mod = {
    async disconnect(d) {
        logger_1.default.log(d.data, d.id, d.params, 'remove hook');
    },
    async guestLogin(d) {
        // 游客登录
        if (!d.data.guest || !d.data.sid)
            return d;
        d.params.anonymous = true;
        const _id = new bson_1.ObjectID().toString();
        const { sid, nickname } = d.data;
        const user = { _id, sid, nickname, role: 'student', anonymous: true };
        d.params.user = d.params.connection.user = user;
        // 加入教室推送组
        // d.app.channel(`class/${sid}`).join(d.app.connection)
        d.result = await d.app.service('authentication').create({ strategy: 'guest' }, { payload: { sid }, user });
    },
    async syncJWT(d) {
        var _a;
        // jwt同步登录
        const { sid, token } = d.data;
        if (!token)
            return d;
        if (/^([a-f0-9]+)$/.test(token))
            return d;
        const { secret } = d.app.get('authentication');
        const data = jsonwebtoken_1.default.verify(d.data.token, secret);
        if (!data.username)
            return d;
        const user = await d.app.service('users').findByMail(data.username);
        logger_1.default.log(data, user, 111, 'syncJWT');
        // 保护字段
        d.params.user = d.params.connection.user = user;
        d.result = await d.app.service('authentication').create({ strategy: 'sync' }, {
            payload: { sid },
            user: { ...user, id: user._id },
        });
        if ((_a = d.result) === null || _a === void 0 ? void 0 : _a.accessToken)
            d.service.patch(null, { on: 'connect', from: 'syncJWT', _sid: sid, user });
    },
    // syncData 老师端，dash与project之间 commit事件同步通知，student模式下不同步，数据不缓存，不可恢复
    // 老师端，dash与project之间 store的对象同步通知，student模式下不同步，数据会缓存，可恢复
    async syncUI(d) {
        var _a, _b;
        // UI sync
        if (d.id !== 'syncUI')
            return d;
        const sid = (_b = (_a = d.params.authentication) === null || _a === void 0 ? void 0 : _a.payload) === null || _b === void 0 ? void 0 : _b.sid;
        const redis = d.app.get('redis');
        Object.keys(d.data).map((key) => {
            redis.hSet(`syncUI:${sid}`, key, JSON.stringify(d.data[key]));
        });
        d.data.id = d.id;
        logger_1.default.warn('syncUI', sid, d.data);
    },
    // 老师端，dash与project之间数据同步commit事件通知，student模式下不同步，数据会缓存，可恢复
    async syncTeacher(d) {
        var _a, _b;
        // 老师同步
        if (d.id !== 'syncTeacher')
            return d;
        const sid = (_b = (_a = d.params.authentication) === null || _a === void 0 ? void 0 : _a.payload) === null || _b === void 0 ? void 0 : _b.sid;
        const commit = d.data.commit;
        d.data.act = d.id;
        d.result = d.data;
        if (!sid || !commit)
            return d;
        const redis = d.app.get('redis');
        const pkey = 'pageConf:' + sid;
        const val = d.data.data;
        // 切换页面的时候
        if (commit === 'student/setCurrentPageIndex') {
            const arr = await redis.HKEYS(pkey);
            // 翻页的时候清空case study数据
            for (const key of arr) {
                if (!key.includes('student/updateAnswerStarOrResponse'))
                    continue;
                redis.HDEL(pkey, key);
            }
        }
        // 保存页面配置
        let key = Array.isArray(val) && Number.isInteger(val[0]) ? `${commit}.${val[0]}` : commit;
        // case study 交互, 多数据保存
        if (commit === 'student/updateAnswerStarOrResponse') {
            key = `${commit}.${val._id}`;
        }
        await redis.HSET(pkey, key, JSON.stringify(val));
        if (key.includes('/show'))
            redis.HDEL(pkey, key.replace('/show', '/hide'));
        if (key.includes('/hide'))
            redis.HDEL(pkey, key.replace('/hide', '/show'));
    },
};
exports.default = {
    before: {
        all: [],
        find: [],
        get: [
            hook_1.default.toClass,
            (d) => {
                if (hook_1.default.classExist(d))
                    return d;
                if (modGet_1.default[d.id])
                    return modGet_1.default[d.id](d);
            },
        ],
        create: [mod.guestLogin, mod.syncJWT],
        update: [],
        patch: [
            (d) => {
                var _a;
                const auth = d.params.authentication;
                if (!auth)
                    return d;
                if (!auth.accessToken)
                    return d;
                if (!auth.payload)
                    auth.payload = jsonwebtoken_1.default.decode(auth.accessToken || '');
                if (d.data.on) {
                    d.data.__sid = d.params.sid;
                    d.data.user = (_a = d.data.user) !== null && _a !== void 0 ? _a : d.params.user;
                }
            },
            mod.syncTeacher,
            mod.syncUI,
        ],
        remove: [mod.disconnect],
    },
    after: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
