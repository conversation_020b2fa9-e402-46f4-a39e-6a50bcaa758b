"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'subjects';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const outline6 = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
    });
    const outline5 = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
        child: [outline6],
    });
    const outline4 = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
        child: [outline5],
    });
    const outline3 = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
        child: [outline4],
    });
    const outline2 = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
        child: [outline3],
    });
    const outline1 = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
        child: [outline2],
    });
    const outline = new Schema({
        name: { type: String, trim: true },
        grade: { type: [String] },
        tags: { type: [String], trim: true },
        code: { type: String, trim: true },
        mark: { type: String, trim: true },
        child: [outline1],
    });
    const schema = new Schema({
        uid: { type: String, index: true, required: true },
        name: { type: String, required: true, trim: true },
        subtitle: { type: String, trim: true },
        curriculum: { type: [String], required: true },
        subjectCode: { type: String },
        participants: { type: String, sparse: true, enum: Agl.subjectsParticipants },
        grade: { type: [String], trim: true },
        del: { type: Boolean, default: false },
        count: {
            unit: { type: Number },
            task: { type: Number },
            classes: { type: Number },
            standard: { type: [Number] },
            topic: { type: [Number] },
        },
        standardLevel: { type: [String], trim: true },
        topicLevel: { type: [String], trim: true },
        standard: [outline],
        topic: [outline],
        code: {
            standard: { type: String, trim: true },
            topic: { type: String, trim: true },
        },
        source: {
            // import sys data
            standardCurriculum: { type: [String] },
            standardSet: { type: [String] },
            topicCurriculum: { type: [String] },
            topicSet: { type: [String] }, // curriculum.title + subject.title
        },
        publish: { type: [String] },
        snapshot: { type: Schema.Types.Mixed },
        coordinator: { type: [String], trim: true }, // 学科组长 uid
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
