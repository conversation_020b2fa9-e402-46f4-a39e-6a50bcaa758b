"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
const hook_1 = __importDefault(require("../../hook"));
const errors_1 = require("@feathersjs/errors");
const search = require('feathers-mongodb-fuzzy-search');
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [
            (d) => {
                const { query, user } = d.params;
                query.uid = user._id;
            },
            search({
                fields: ['name'],
            }),
        ],
        get: [
            hook_1.default.toClass,
            async (d) => {
                var _a;
                if (/^\d+$/.test(d.id + '')) {
                    const { _id } = (_a = d.params.user) !== null && _a !== void 0 ? _a : {};
                    const rs = await d.service.Model.findOne({ rid: d.id, uid: _id });
                    d.result = rs;
                }
                return d;
            },
        ],
        create: [
            async (d) => {
                var _a;
                d.data.uid = (_a = d.params.user) === null || _a === void 0 ? void 0 : _a._id;
                const doc = await d.app.service('unit').Model.findById(d.data.rid).select('name');
                if (!doc)
                    return Promise.reject(new errors_1.NotFound());
                d.data.name = doc.name;
            },
        ],
        update: [],
        patch: [],
        remove: [],
    },
    after: {
        all: [],
        find: [
            async (d) => {
                if (!d.result.data)
                    return;
                await d.service.extList(d.result);
            },
        ],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
