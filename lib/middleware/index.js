"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = __importDefault(require("../logger"));
const fs_1 = __importDefault(require("fs"));
const child_process_1 = require("child_process");
const https_1 = __importDefault(require("https"));
const pdfkit_1 = __importDefault(require("./pdfkit"));
const maps_1 = __importDefault(require("./maps"));
const sqlup_1 = __importDefault(require("./sqlup"));
// import cron from './cron';
const slides_1 = __importDefault(require("./slides"));
const mail_1 = __importDefault(require("./mail"));
const google_1 = __importDefault(require("./google"));
const proxy_1 = __importDefault(require("./proxy"));
const ssr_1 = __importDefault(require("./ssr"));
const zoom_1 = __importDefault(require("./zoom"));
const paypal_1 = __importDefault(require("./paypal"));
const activeParent_1 = __importDefault(require("./activeParent"));
// import busboy from 'busboy';
// Don't remove this comment. It's needed to format import lines nicely.
// eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
function default_1(app) {
    (0, pdfkit_1.default)(app);
    (0, maps_1.default)(app);
    // cron(app)
    (0, sqlup_1.default)(app);
    (0, slides_1.default)(app);
    (0, mail_1.default)(app);
    (0, proxy_1.default)(app);
    (0, google_1.default)(app);
    (0, ssr_1.default)(app);
    (0, zoom_1.default)(app);
    (0, paypal_1.default)(app);
    (0, activeParent_1.default)(app);
    const git = {};
    if (fs_1.default.existsSync('.git/FETCH_HEAD')) {
        git.fetch_head = fs_1.default.readFileSync('.git/FETCH_HEAD', { encoding: 'utf-8' });
        git.head = git.fetch_head.split('\t')[0];
        // git.commit = fs.readFileSync('.git/COMMIT_EDITMSG', { encoding: 'utf-8' })
    }
    app.all('/tool/gitPull', async (req, res) => {
        app.get('redis').publish('gitPull', JSON.stringify(req.body));
        res.json({ ok: 1 });
    });
    app.get('/tool/wget', async (req, res) => {
        if (!req.query.url)
            return res.json({ n: 0 });
        https_1.default.get(req.query.url, (r) => {
            r.pipe(res);
        });
    });
    app.get('/tool/exec', async (req, res) => {
        const arr = ['yarn prisma generate', 'yarn install', 'git pull', 'yarn run compile'];
        const cmd = req.query.cmd;
        if (!arr.includes(cmd))
            return res.send(arr.map((o) => `<a href="?cmd=${o}">${o}</a>`).join('<br>'));
        (0, child_process_1.exec)(cmd, (error, stdout, stderr) => {
            res.send(`<div>${cmd}</div>
      <pre style="color:red;">${error}</pre>
      <pre style="color:green;">${stdout}</pre>
      <pre style="color:pink;">${stderr}</pre>`);
        });
    });
    app.get('/version', async (req, res) => {
        if (!fs_1.default.existsSync('.git/FETCH_HEAD'))
            return res.json({ ok: 1, cwd: process.cwd() });
        const fetch_head = fs_1.default.readFileSync('.git/FETCH_HEAD', { encoding: 'utf-8' });
        const head = fetch_head.split('\t')[0];
        // const commit = fs.readFileSync('.git/COMMIT_EDITMSG', { encoding: 'utf-8' })
        res.json({ ok: 1, pid: process.pid, git, head, fetch_head, uptime: process.uptime(), port: app.get('port') });
        if (!isDev && git.head === head)
            return;
        if (req.query.restart) {
            logger_1.default.warn('restart node', process.env.NODE_ENV);
            await sleep(100);
            Ser.close();
            logger_1.default.warn('server close');
            await sleep(5000);
            logger_1.default.warn('process exit');
            process.exit();
        }
    });
}
exports.default = default_1;
