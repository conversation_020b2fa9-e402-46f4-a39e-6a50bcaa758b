"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'curric';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        school: { type: String, required: true },
        code: { type: String },
        name: { type: String, required: true, trim: true },
        subjects: { type: [String], trim: true },
        count: {
            subjects: { type: Number, default: 0 },
            skills: { type: Number, default: 0 },
            tags: { type: Number, default: 0 },
        },
        unit: { type: [String], trim: true },
        task: { type: [String], trim: true },
        del: { type: Boolean, default: false },
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
