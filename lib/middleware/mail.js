"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const logger_1 = __importDefault(require("../logger"));
const nodemailer = require('nodemailer');
const fileUpload = require('express-fileupload');
function default_1(app) {
    let email;
    app.post('/sendMail', fileUpload(), async (req, res) => {
        var _a, _b;
        const redisClient = app.get('redis');
        email = JSON.parse((_a = await redisClient.SRANDMEMBER('smtp')) !== null && _a !== void 0 ? _a : null);
        let { mail, title, body } = req.body || req.query;
        const attachments = [];
        if (req.files) {
            for (const file of (Array.isArray(req.files.files) ? req.files.files : [req.files.files])) {
                attachments.push({ filename: file.name, content: file.data });
            }
        }
        let rs = {};
        body = body.replace(/\/\/admin\.classcipe\.com\//g, '//classcipe.com/cc-admin/')
            .replace(/\/\/dev\.classcipe\.com\//g, `//${req.hostname}/`);
        const doc = { type: 'mail', data: { mail, title, body, attachments: attachments.map((v) => { v.size = v.content.lenght; delete v.content; return v; }) } };
        if (email) {
            rs = await sendMail(mail, title, body, attachments);
            doc.status = ((_b = rs === null || rs === void 0 ? void 0 : rs.response) === null || _b === void 0 ? void 0 : _b.substring(0, 4)) === '250 ';
            doc.conf = [email.from, email.user, email.host];
            doc.rs = rs;
        }
        app.service('notice').create(doc).then();
        res.json(rs);
    });
    const sendMail = async (to, title, body, attachments) => {
        let opt = {
            host: email.host,
            port: parseInt(email.port || 587),
            secure: false, // true for 465, false for other ports
        };
        if (email.user) {
            opt.secure = true;
            opt.auth = { user: email.user, pass: email.pass };
        }
        let transporter = nodemailer.createTransport(opt);
        let mailOptions = {
            from: '"Classcipe" <' + email.from + '>',
            to,
            subject: title || 'Hello ✔ test mail title',
            // text: 'Hello world?', // plain text body
            html: body || '<b>Hello world? This is test mail content</b>',
            attachments
        };
        logger_1.default.warn(opt, mailOptions);
        return new Promise((res, rej) => {
            transporter.sendMail(mailOptions, (error, info) => {
                if (error) {
                    logger_1.default.warn('sendFaild', error);
                    return res(error);
                }
                logger_1.default.warn('Message sent: %s', info.messageId, info.response, info.accepted, info.rejected);
                res(info);
            });
        });
    };
}
exports.default = default_1;
