"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'interactiveVideoes';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        uid: { type: String },
        tid: { type: String },
        videoId: { type: String, index: true },
        startTime: { type: Number, trim: true },
        endTime: { type: Number, trim: true },
        trimOptions: {
            startTime: { type: Number, trim: true },
            endTime: { type: Number, trim: true }, //end trim time
        },
        isCopy: { type: Boolean },
        type: { type: String, trim: true },
        thumbnail: { type: Schema.Types.Mixed },
        name: { type: String, trim: true },
        isDeleted: { type: Boolean, default: false },
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
