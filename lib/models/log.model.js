"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'log';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        ip: { type: String, required: true, index: true },
        method: { type: String },
        ttl: { type: Number },
        body: { type: Schema.Types.Mixed },
        headers: { type: Schema.Types.Mixed },
        status: { type: Number, index: true },
        origin: { type: String, index: true },
        pathname: { type: String, index: true },
        cookie: { type: String },
        date: { type: Date, index: true },
        type: { type: String, index: true },
        msg: { type: String, index: true },
        num: { type: String },
        line: { type: String },
        lang: { type: String },
        stack: { type: String },
        ua: { type: String },
        uid: { type: String },
        nickname: { type: String },
        email: { type: String },
        href: { type: String },
        ref: { type: String },
        exp: { type: Date },
        schoolId: { type: String },
        userMode: { type: String },
        schoolName: { type: String },
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
