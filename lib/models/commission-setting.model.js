"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'commissionSetting';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        tab: { type: String, enum: ['earn', 'claim'], default: 'earn' },
        role: { type: String, required: true, enum: ['agency', 'organization', 'education_consultant', 'classcipe_staff'] },
        category: {
            type: String,
            required: true,
            enum: [
                'task',
                'service',
                'service_premium',
                'session',
                'saas_tool_paid',
                'unit',
                'verify',
                'service_substitute',
                'service_correct',
                'self_study', // 自学习
            ],
        },
        categoryType: { type: String },
        mode: { type: String, required: true, default: 'percentage', enum: ['fixed', 'percentage'] },
        value: { type: Number, required: true }, //固定数值;按比例
    }, {
        timestamps: true,
    });
    schema.index({ tab: 1, role: 1, category: 1, categoryType: 1 }, { unique: true });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
