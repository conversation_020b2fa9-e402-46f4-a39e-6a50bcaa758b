"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'comments';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        sid: { type: String },
        rid: { type: String, required: true },
        page: { type: String, required: true },
        quizId: { type: String },
        // receiver
        to: { type: String, required: true },
        receiver: { type: String, required: true },
        otype: { type: String, required: true },
        otext: { type: String, trim: true },
        // sender
        from: { type: String, required: true },
        sender: { type: String, required: true },
        review: { type: String, required: true },
        point: { type: Schema.Types.Mixed },
        read: { type: Boolean, default: false }, // read status
    }, {
        timestamps: true,
    });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
