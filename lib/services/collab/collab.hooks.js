"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const authentication = __importStar(require("@feathersjs/authentication"));
const hook_1 = __importDefault(require("../../hook"));
// Don't remove this comment. It's needed to format import lines nicely.
const { authenticate } = authentication.hooks;
exports.default = {
    before: {
        all: [authenticate('jwt')],
        find: [],
        get: [
            hook_1.default.toClass,
            async (d) => {
                var _a, _b;
                const { type } = (_a = d.params.query) !== null && _a !== void 0 ? _a : {};
                if (!type)
                    return d; // return collab doc
                else { // find by rid
                    let old = await d.service.Model.findOne({ rid: d.id });
                    if (!old)
                        old = await d.service.Model.create({ rid: d.id, type, uid: (_b = d.params.user) === null || _b === void 0 ? void 0 : _b._id });
                    d.result = Acan.clone(old);
                }
            },
        ],
        create: [],
        update: [],
        patch: [
            async (d) => {
                var _a, _b, _c, _d;
                if ((_b = (_a = d.data.$pull) === null || _a === void 0 ? void 0 : _a.members) === null || _b === void 0 ? void 0 : _b._id)
                    await d.service.shareRemove((_d = (_c = d.data.$pull) === null || _c === void 0 ? void 0 : _c.members) === null || _d === void 0 ? void 0 : _d._id, d.params);
                if (!Acan.isEmpty(d.data.email))
                    return d.result = await d.service.batchInvite(d.id, d.data, d.params), d;
            },
        ],
        remove: [],
    },
    after: {
        all: [],
        find: [],
        get: [async (d) => {
                var _a;
                const { members, guest, uid } = d.result;
                const { _id, email } = (_a = d.params.user) !== null && _a !== void 0 ? _a : {};
                if (members && guest && _id && uid !== _id) {
                    const member = Acan.clone(members).find((one) => one.email === email);
                    if (!member) {
                        d.result = await d.service.autoJoin(d.result, d.params);
                    }
                }
            }],
        create: [],
        update: [],
        patch: [async (d) => {
                if (d.data['members.$.role']) {
                    await d.service.shareUpRole(d.result, d.params);
                }
            }],
        remove: [],
    },
    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    },
};
