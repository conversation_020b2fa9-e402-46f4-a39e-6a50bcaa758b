"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
function default_1(app) {
    app.use('/translate', {
        async create(data, _params) {
            const { text, target } = data;
            const googleConfig = app.get('googleTranslate');
            console.log('GOOGLE_API_KEY', googleConfig.key);
            const GOOGLE_API_KEY = googleConfig.key;
            if (!text || !target) {
                throw new Error('Missing required fields: "text" and "target"');
            }
            try {
                const response = await axios_1.default.post('https://translation.googleapis.com/language/translate/v2', {}, {
                    params: {
                        q: text,
                        target,
                        key: GOOGLE_API_KEY,
                    },
                });
                const translatedText = response.data.data.translations[0].translatedText;
                return { translatedText };
            }
            catch (error) {
                throw new Error('Translation failed');
            }
        },
    });
}
exports.default = default_1;
