"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function default_1(app) {
    const modelName = 'rooms';
    const mongooseClient = app.get('mongooseClient');
    const { Schema } = mongooseClient;
    const schema = new Schema({
        sid: { type: String, required: true },
        members: [
            {
                _id: { type: String, required: true },
                nickname: { type: String, required: true },
                avatar: { type: String },
                email: { type: String },
                group: { type: String },
                feel: { type: String },
                last: { type: Date, default: Date.now },
            },
        ],
        groups: [
            {
                name: { type: String }, // 组名
            },
        ],
        groupMax: { type: Number, default: 10 },
        attend: { type: [String] },
        block: { type: [String] },
        teachers: [
            {
                _id: { type: String, required: true },
                nickname: { type: String, required: true },
                avatar: { type: String },
                email: { type: String },
                last: { type: Date, default: Date.now },
            },
        ],
    }, {
        timestamps: true,
    });
    schema.index({ 'members._id': 1, sid: -1 }, { unique: true });
    // This is necessary to avoid model compilation errors in watch mode
    // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
    if (mongooseClient.modelNames().includes(modelName)) {
        ;
        mongooseClient.deleteModel(modelName);
    }
    return mongooseClient.model(modelName, schema);
}
exports.default = default_1;
